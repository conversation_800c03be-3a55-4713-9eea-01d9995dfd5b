# 企业微信自动回复系统 - 完整打包版

## 包含版本

### 1. exe版本 (推荐)
- 位置：`exe版本/` 目录
- 特点：完全独立运行，无需安装Python
- 使用：直接运行 `企业微信自动回复.exe` 或 `start.bat`

### 2. 便携版本
- 位置：`便携版本/` 目录  
- 特点：需要Python环境，但更灵活
- 使用：运行 `start.bat` 或 `python gui_app.py`

## 推荐使用

**建议优先使用exe版本**，因为：
- ✅ 无需安装Python环境
- ✅ 完全独立运行
- ✅ 内置所有依赖包括ntwork库
- ✅ 启动更简单

## 系统要求

### exe版本
- Windows 10/11
- 企业微信客户端

### 便携版本  
- Windows 10/11
- Python 3.10+
- 企业微信客户端

## 使用说明

1. 选择合适的版本（推荐exe版本）
2. 确保企业微信客户端已启动并登录
3. 运行对应的启动脚本或exe文件
4. 在GUI界面中配置参数
5. 启动服务开始使用

## 注意事项

- 两个版本功能完全相同
- 都内置了ntwork库，无需单独安装
- 建议在测试群中先测试功能
- 如有问题，可以尝试另一个版本

## 技术支持

如遇问题请检查：
1. 企业微信客户端是否正常运行
2. 配置文件是否正确设置
3. 查看程序运行日志
