#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目打包脚本
自动化打包流程，将ntwork库作为外部依赖
"""

import os
import sys
import subprocess
import shutil
import json
from pathlib import Path

def check_dependencies():
    """检查打包依赖"""
    print("🔍 检查打包依赖...")
    
    try:
        import PyInstaller
        print(f"✅ PyInstaller 版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller 未安装")
        print("请运行: pip install pyinstaller")
        return False
    
    try:
        import PyQt5
        print(f"✅ PyQt5 已安装")
    except ImportError:
        print("❌ PyQt5 未安装")
        print("请运行: pip install PyQt5")
        return False
    
    return True

def create_dependencies_setup():
    """创建所有依赖的安装脚本"""
    print("📝 创建依赖安装脚本...")

    # 创建依赖安装脚本
    setup_script = '''@echo off
chcp 65001 >nul
echo 正在安装企业微信自动回复系统依赖...
echo.

echo [1/4] 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] 未找到Python环境，请先安装Python 3.10+
    pause
    exit /b 1
)
echo [OK] Python环境检查通过

echo.
echo [2/4] 安装基础依赖...
pip install PyQt5 requests Pillow dulwich --no-warn-script-location
if %errorlevel% neq 0 (
    echo [ERROR] 基础依赖安装失败
    pause
    exit /b 1
)
echo [OK] 基础依赖安装成功

echo.
echo [3/4] 安装ntwork库...
if exist "ntwork-0.1.3-cp310-cp310-win_amd64.whl" (
    echo 发现本地ntwork wheel文件，正在安装...
    pip install ntwork-0.1.3-cp310-cp310-win_amd64.whl --force-reinstall --no-warn-script-location
    if %errorlevel% equ 0 (
        echo [OK] ntwork库安装成功
    ) else (
        echo [ERROR] ntwork库安装失败
        pause
        exit /b 1
    )
) else (
    echo [ERROR] 未找到ntwork wheel文件
    echo 请确保 ntwork-0.1.3-cp310-cp310-win_amd64.whl 文件存在
    pause
    exit /b 1
)

echo.
echo [4/4] 验证安装...
python -c "import PyQt5; import requests; import PIL; import ntwork; print('[OK] 所有依赖验证通过')" 2>nul
if %errorlevel% neq 0 (
    echo [WARNING] 依赖验证失败，但可能仍然可以运行
)

echo.
echo ========================================
echo 依赖安装完成！
echo 现在可以运行 企业微信自动回复系统.exe
echo ========================================
pause
'''

    with open('install_dependencies.bat', 'w', encoding='utf-8') as f:
        f.write(setup_script)

    # 创建简化的启动脚本
    start_script = '''@echo off
chcp 65001 >nul
echo 启动企业微信自动回复系统...

REM 检查依赖是否已安装
python -c "import PyQt5; import requests; import PIL; import ntwork" 2>nul
if %errorlevel% neq 0 (
    echo [WARNING] 检测到依赖未安装或不完整
    echo 请先运行 install_dependencies.bat 安装依赖
    echo.
    set /p choice="是否现在安装依赖? (y/n): "
    if /i "%choice%"=="y" (
        call install_dependencies.bat
        if %errorlevel% neq 0 exit /b 1
    ) else (
        echo 取消启动
        pause
        exit /b 1
    )
)

echo 正在启动程序...
start "" "企业微信自动回复系统.exe"
'''

    with open('start.bat', 'w', encoding='utf-8') as f:
        f.write(start_script)

    print("✅ 创建了依赖安装和启动脚本")

def build_application():
    """构建应用程序"""
    print("🔨 开始构建应用程序...")
    
    # 生成spec文件
    print("📝 生成PyInstaller配置文件...")
    result = subprocess.run([sys.executable, 'build_spec.py'], capture_output=True, text=True)
    if result.returncode != 0:
        print(f"❌ 生成spec文件失败: {result.stderr}")
        return False
    
    # 清理之前的构建
    if os.path.exists('dist'):
        print("🧹 清理之前的构建文件...")
        shutil.rmtree('dist')
    
    if os.path.exists('build'):
        shutil.rmtree('build')
    
    # 运行PyInstaller
    print("🚀 运行PyInstaller...")
    cmd = [sys.executable, '-m', 'PyInstaller', 'gui_app.spec', '--clean']
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"❌ PyInstaller构建失败:")
        print(result.stderr)
        return False
    
    print("✅ PyInstaller构建成功")
    return True

def copy_external_files():
    """复制外部依赖文件"""
    print("📁 复制外部依赖文件...")

    dist_dir = Path('dist/企业微信自动回复系统')
    if not dist_dir.exists():
        print("❌ 构建目录不存在")
        return False

    # 复制ntwork wheel文件
    ntwork_wheel = 'ntwork-0.1.3-cp310-cp310-win_amd64.whl'
    if os.path.exists(ntwork_wheel):
        shutil.copy2(ntwork_wheel, dist_dir)
        print(f"✅ 复制了 {ntwork_wheel}")
    else:
        print(f"⚠️ 未找到 {ntwork_wheel} 文件")

    # 复制依赖安装脚本
    scripts_to_copy = ['install_dependencies.bat', 'start.bat']
    for script in scripts_to_copy:
        if os.path.exists(script):
            shutil.copy2(script, dist_dir)
            print(f"✅ 复制了 {script}")

    # 复制配置文件
    if os.path.exists('config.json'):
        shutil.copy2('config.json', dist_dir)
        print("✅ 复制了 config.json")
    elif os.path.exists('config-template.json'):
        shutil.copy2('config-template.json', dist_dir / 'config.json')
        print("✅ 复制了配置模板为 config.json")

    # 创建requirements.txt用于依赖安装
    requirements_content = '''PyQt5>=5.15.0
requests>=2.25.0
Pillow>=8.0.0
dulwich>=0.20.0
'''

    with open(dist_dir / 'requirements.txt', 'w', encoding='utf-8') as f:
        f.write(requirements_content)

    # 创建使用说明
    readme_content = '''# 企业微信自动回复系统

## 快速开始：

方式一（推荐）：
1. 双击运行 `start.bat` - 自动检查并安装依赖，然后启动程序

方式二（手动）：
1. 双击运行 `install_dependencies.bat` 安装所有依赖
2. 编辑 `config.json` 配置文件
3. 双击运行 `企业微信自动回复系统.exe`

## 注意事项：

- 确保已安装Python 3.10+环境
- 确保企业微信客户端已安装并登录
- 所有复杂依赖都作为外部文件，不打包在exe中
- 配置文件需要根据实际情况修改

## 文件说明：

- `企业微信自动回复系统.exe` - 主程序（轻量化）
- `start.bat` - 一键启动脚本（推荐使用）
- `install_dependencies.bat` - 依赖安装脚本
- `ntwork-0.1.3-cp310-cp310-win_amd64.whl` - ntwork库文件
- `requirements.txt` - Python依赖列表
- `config.json` - 配置文件
- `_internal/` - 程序核心文件目录

## 依赖说明：

外部依赖（需要单独安装）：
- PyQt5 - GUI界面库
- requests - HTTP请求库
- Pillow - 图像处理库
- dulwich - Git操作库
- ntwork - 企业微信接口库

## 技术支持：

如遇问题请检查：
1. Python环境是否正确安装
2. 企业微信客户端是否正常运行
3. 依赖是否正确安装（运行install_dependencies.bat）
4. 配置文件是否正确设置
'''

    with open(dist_dir / 'README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)

    print("✅ 创建了使用说明文件")
    return True

def main():
    """主函数"""
    print("🚀 企业微信自动回复系统打包工具")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        return False
    
    # 创建依赖安装脚本
    create_dependencies_setup()
    
    # 构建应用
    if not build_application():
        return False
    
    # 复制外部文件
    if not copy_external_files():
        return False
    
    print("\n" + "=" * 60)
    print("🎉 轻量化打包完成！")
    print(f"📁 输出目录: dist/企业微信自动回复系统/")
    print("\n✨ 特点:")
    print("- exe文件轻量化，所有复杂依赖作为外部文件")
    print("- 支持一键安装依赖和启动")
    print("- 便于维护和更新")
    print("\n🚀 使用说明:")
    print("方式一（推荐）:")
    print("  1. 进入 dist/企业微信自动回复系统/ 目录")
    print("  2. 双击运行 start.bat（自动安装依赖并启动）")
    print("\n方式二（手动）:")
    print("  1. 进入 dist/企业微信自动回复系统/ 目录")
    print("  2. 运行 install_dependencies.bat 安装所有依赖")
    print("  3. 配置 config.json 文件")
    print("  4. 运行 企业微信自动回复系统.exe")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
