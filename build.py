#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目打包脚本
自动化打包流程，将ntwork库作为外部依赖
"""

import os
import sys
import subprocess
import shutil
import json
from pathlib import Path

def check_dependencies():
    """检查打包依赖"""
    print("🔍 检查打包依赖...")
    
    try:
        import PyInstaller
        print(f"✅ PyInstaller 版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller 未安装")
        print("请运行: pip install pyinstaller")
        return False
    
    try:
        import PyQt5
        print(f"✅ PyQt5 已安装")
    except ImportError:
        print("❌ PyQt5 未安装")
        print("请运行: pip install PyQt5")
        return False
    
    return True

def create_ntwork_setup():
    """创建ntwork库的安装脚本"""
    print("📝 创建ntwork库安装脚本...")
    
    setup_script = '''@echo off
echo 正在安装ntwork库...
echo.

REM 检查是否存在ntwork wheel文件
if exist "ntwork-0.1.3-cp310-cp310-win_amd64.whl" (
    echo 发现本地ntwork wheel文件，正在安装...
    pip install ntwork-0.1.3-cp310-cp310-win_amd64.whl --force-reinstall
    if %errorlevel% equ 0 (
        echo [OK] ntwork库安装成功
    ) else (
        echo [ERROR] ntwork库安装失败
        pause
        exit /b 1
    )
) else (
    echo [ERROR] 未找到ntwork wheel文件
    echo 请确保 ntwork-0.1.3-cp310-cp310-win_amd64.whl 文件存在
    pause
    exit /b 1
)

echo.
echo ntwork库安装完成！
echo 现在可以运行企业微信自动回复系统了
pause
'''

    with open('install_ntwork.bat', 'w', encoding='utf-8') as f:
        f.write(setup_script)
    
    print("✅ 创建了 install_ntwork.bat 安装脚本")

def build_application():
    """构建应用程序"""
    print("🔨 开始构建应用程序...")
    
    # 生成spec文件
    print("📝 生成PyInstaller配置文件...")
    result = subprocess.run([sys.executable, 'build_spec.py'], capture_output=True, text=True)
    if result.returncode != 0:
        print(f"❌ 生成spec文件失败: {result.stderr}")
        return False
    
    # 清理之前的构建
    if os.path.exists('dist'):
        print("🧹 清理之前的构建文件...")
        shutil.rmtree('dist')
    
    if os.path.exists('build'):
        shutil.rmtree('build')
    
    # 运行PyInstaller
    print("🚀 运行PyInstaller...")
    cmd = [sys.executable, '-m', 'PyInstaller', 'gui_app.spec', '--clean']
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"❌ PyInstaller构建失败:")
        print(result.stderr)
        return False
    
    print("✅ PyInstaller构建成功")
    return True

def copy_external_files():
    """复制外部依赖文件"""
    print("📁 复制外部依赖文件...")
    
    dist_dir = Path('dist/企业微信自动回复系统')
    if not dist_dir.exists():
        print("❌ 构建目录不存在")
        return False
    
    # 复制ntwork wheel文件
    ntwork_wheel = 'ntwork-0.1.3-cp310-cp310-win_amd64.whl'
    if os.path.exists(ntwork_wheel):
        shutil.copy2(ntwork_wheel, dist_dir)
        print(f"✅ 复制了 {ntwork_wheel}")
    else:
        print(f"⚠️ 未找到 {ntwork_wheel} 文件")
    
    # 复制安装脚本
    if os.path.exists('install_ntwork.bat'):
        shutil.copy2('install_ntwork.bat', dist_dir)
        print("✅ 复制了 install_ntwork.bat")
    
    # 复制配置文件
    if os.path.exists('config.json'):
        shutil.copy2('config.json', dist_dir)
        print("✅ 复制了 config.json")
    elif os.path.exists('config-template.json'):
        shutil.copy2('config-template.json', dist_dir / 'config.json')
        print("✅ 复制了配置模板为 config.json")
    
    # 创建使用说明
    readme_content = '''# 企业微信自动回复系统

## 首次使用步骤：

1. 双击运行 `install_ntwork.bat` 安装ntwork库
2. 编辑 `config.json` 配置文件
3. 双击运行 `企业微信自动回复系统.exe`

## 注意事项：

- 确保企业微信客户端已安装并登录
- ntwork库需要单独安装，不包含在exe中
- 配置文件需要根据实际情况修改

## 文件说明：

- `企业微信自动回复系统.exe` - 主程序
- `install_ntwork.bat` - ntwork库安装脚本
- `ntwork-0.1.3-cp310-cp310-win_amd64.whl` - ntwork库文件
- `config.json` - 配置文件
- `_internal/` - 程序依赖文件目录

## 技术支持：

如遇问题请检查：
1. 企业微信客户端是否正常运行
2. ntwork库是否正确安装
3. 配置文件是否正确设置
'''
    
    with open(dist_dir / 'README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 创建了使用说明文件")
    return True

def main():
    """主函数"""
    print("🚀 企业微信自动回复系统打包工具")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        return False
    
    # 创建ntwork安装脚本
    create_ntwork_setup()
    
    # 构建应用
    if not build_application():
        return False
    
    # 复制外部文件
    if not copy_external_files():
        return False
    
    print("\n" + "=" * 50)
    print("🎉 打包完成！")
    print(f"📁 输出目录: dist/企业微信自动回复系统/")
    print("\n使用说明:")
    print("1. 进入 dist/企业微信自动回复系统/ 目录")
    print("2. 先运行 install_ntwork.bat 安装ntwork库")
    print("3. 配置 config.json 文件")
    print("4. 运行 企业微信自动回复系统.exe")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
