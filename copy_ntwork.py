#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
复制ntwork库到项目目录的脚本
"""

import os
import sys
import shutil
import site
from pathlib import Path

def find_ntwork_location():
    """查找ntwork库的安装位置"""
    try:
        import ntwork
        ntwork_path = Path(ntwork.__file__).parent
        print(f"找到ntwork库位置: {ntwork_path}")
        return ntwork_path
    except ImportError:
        print("未找到ntwork库，请先安装")
        return None

def copy_ntwork_to_project():
    """复制ntwork库到项目目录"""
    print("开始复制ntwork库...")
    
    # 查找ntwork库位置
    ntwork_path = find_ntwork_location()
    if not ntwork_path:
        return False
    
    # 目标目录
    target_dir = Path('dist/企业微信自动回复系统_便携版')
    if not target_dir.exists():
        print(f"目标目录不存在: {target_dir}")
        return False
    
    # 复制ntwork库
    target_ntwork = target_dir / 'ntwork'
    if target_ntwork.exists():
        print("删除已存在的ntwork目录...")
        shutil.rmtree(target_ntwork)
    
    print(f"复制ntwork库到: {target_ntwork}")
    shutil.copytree(ntwork_path, target_ntwork)
    
    # 查找并复制相关的依赖库
    site_packages = ntwork_path.parent
    
    # 常见的ntwork相关依赖
    dependencies = [
        'win32gui.pyd',
        'win32api.pyd', 
        'win32con.pyd',
        'pywintypes310.dll',
        'pythoncom310.dll'
    ]
    
    print("查找并复制相关依赖...")
    for dep in dependencies:
        dep_path = site_packages / dep
        if dep_path.exists():
            target_dep = target_dir / dep
            shutil.copy2(dep_path, target_dep)
            print(f"复制依赖: {dep}")
        else:
            # 在更深层目录中查找
            for root, dirs, files in os.walk(site_packages):
                if dep in files:
                    src_file = Path(root) / dep
                    target_dep = target_dir / dep
                    shutil.copy2(src_file, target_dep)
                    print(f"复制依赖: {dep} (从 {src_file})")
                    break
    
    print("ntwork库复制完成！")
    return True

def create_ntwork_init():
    """创建ntwork库的初始化脚本"""
    target_dir = Path('dist/企业微信自动回复系统_便携版')
    
    # 创建一个启动时添加ntwork路径的脚本
    init_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ntwork库路径初始化脚本
"""

import sys
import os

def init_ntwork_path():
    """初始化ntwork库路径"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    ntwork_path = os.path.join(current_dir, 'ntwork')
    
    if os.path.exists(ntwork_path) and ntwork_path not in sys.path:
        sys.path.insert(0, current_dir)
        print(f"已添加ntwork库路径: {ntwork_path}")

if __name__ == "__main__":
    init_ntwork_path()
'''
    
    with open(target_dir / 'init_ntwork.py', 'w', encoding='utf-8') as f:
        f.write(init_script)
    
    print("创建了ntwork初始化脚本")

def update_gui_app():
    """更新gui_app.py以包含ntwork路径初始化"""
    target_dir = Path('dist/企业微信自动回复系统_便携版')
    gui_app_path = target_dir / 'gui_app.py'
    
    if not gui_app_path.exists():
        print("gui_app.py不存在，跳过更新")
        return
    
    # 读取原文件
    with open(gui_app_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 在文件开头添加ntwork路径初始化
    init_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 初始化ntwork库路径
import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

'''
    
    # 如果还没有添加初始化代码，则添加
    if "初始化ntwork库路径" not in content:
        # 找到第一个import语句的位置
        lines = content.split('\n')
        insert_pos = 0
        
        # 跳过开头的注释和空行
        for i, line in enumerate(lines):
            if line.strip() and not line.strip().startswith('#'):
                insert_pos = i
                break
        
        # 插入初始化代码
        lines.insert(insert_pos, init_code.strip())
        content = '\n'.join(lines)
        
        # 写回文件
        with open(gui_app_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("已更新gui_app.py，添加ntwork路径初始化")

def create_updated_start_script():
    """创建更新的启动脚本"""
    target_dir = Path('dist/企业微信自动回复系统_便携版')
    
    start_script = '''@echo off
chcp 65001 >nul
echo 启动企业微信自动回复系统...

REM 检查Python环境
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] 未找到Python环境，请先安装Python 3.10+
    pause
    exit /b 1
)

REM 检查基础依赖
python -c "import PyQt5; import requests; import PIL" 2>nul
if %errorlevel% neq 0 (
    echo [WARNING] 检测到基础依赖未安装
    echo 正在尝试安装基础依赖...
    pip install PyQt5 requests Pillow dulwich --no-warn-script-location
    if %errorlevel% neq 0 (
        echo [ERROR] 基础依赖安装失败
        pause
        exit /b 1
    )
)

echo [INFO] ntwork库已内置，无需单独安装
echo 正在启动程序...
python gui_app.py
if %errorlevel% neq 0 (
    echo 程序运行出错，请检查错误信息
    pause
)
'''
    
    with open(target_dir / 'start.bat', 'w', encoding='utf-8') as f:
        f.write(start_script)
    
    print("创建了更新的启动脚本")

def create_updated_readme():
    """创建更新的README文件"""
    target_dir = Path('dist/企业微信自动回复系统_便携版')
    
    readme_content = '''# 企业微信自动回复系统 (内置ntwork版)

## 简介

这是一个内置了ntwork库的企业微信自动回复系统便携版，无需手动安装ntwork库。

## 系统要求

- Windows 10/11
- Python 3.10+ (必须安装)
- 企业微信客户端

## 快速开始

### 方式一：一键启动（推荐）
1. 双击运行 `start.bat`
   - 自动检查并安装基础依赖
   - 启动程序

### 方式二：直接启动
1. 双击运行 `quick_start.bat`
   - 直接启动程序（需要已安装基础依赖）

### 方式三：命令行
```bash
# 安装基础依赖（首次运行）
pip install PyQt5 requests Pillow dulwich

# 启动程序
python gui_app.py
```

## 文件说明

### 启动文件
- `start.bat` - 智能启动脚本（检查依赖+启动）
- `quick_start.bat` - 快速启动脚本（仅启动）

### 程序文件
- `gui_app.py` - 主程序入口
- `config.json` - 配置文件
- `2.ico` - 程序图标
- `ntwork/` - 内置ntwork库目录

### 源码目录
- `bot/` - 机器人模块
- `bridge/` - 桥接模块  
- `channel/` - 通道模块
- `common/` - 公共模块
- `gui/` - 图形界面模块
- `plugins/` - 插件模块
- `translate/` - 翻译模块
- `voice/` - 语音模块

## 特点

- ✅ **内置ntwork库** - 无需手动安装ntwork
- ✅ **便携式设计** - 包含所有必要文件
- ✅ **一键启动** - 自动处理依赖
- ✅ **完整源码** - 便于维护和修改

## 配置说明

编辑 `config.json` 文件进行配置：

```json
{
    "channel_type": "wework",
    "wework_smart": true,
    "model": "mock",
    "auto_reply_text": "你好，我是自动回复机器人"
}
```

## 使用说明

1. **启动企业微信客户端**并登录
2. **运行程序**：双击 `start.bat`
3. **配置设置**：在GUI界面中配置相关参数
4. **开始服务**：点击"启动服务"按钮

## 注意事项

- 确保企业微信客户端已启动并登录
- 首次运行可能需要安装基础依赖
- ntwork库已内置，无需单独安装
- 建议在企业微信测试群中先测试功能

## 故障排除

### 常见问题

1. **Python未安装**
   - 下载安装Python 3.10+: https://www.python.org/downloads/
   - 安装时勾选"Add Python to PATH"

2. **基础依赖安装失败**
   - 检查网络连接
   - 尝试使用国内镜像：`pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/`

3. **企业微信连接失败**
   - 确保企业微信客户端已启动
   - 重启企业微信客户端

4. **程序无法启动**
   - 检查Python版本是否为3.10+
   - 查看错误信息并检查配置文件

## 版本信息

- 版本：内置ntwork版 v1.0
- 特点：内置ntwork库，无需手动安装
- 兼容性：Windows 10/11 + Python 3.10+
'''
    
    with open(target_dir / 'README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("创建了更新的README文件")

def main():
    """主函数"""
    print("企业微信自动回复系统 - ntwork库内置工具")
    print("=" * 50)
    
    # 复制ntwork库
    if not copy_ntwork_to_project():
        print("复制ntwork库失败")
        return False
    
    # 创建初始化脚本
    create_ntwork_init()
    
    # 更新gui_app.py
    update_gui_app()
    
    # 创建更新的启动脚本
    create_updated_start_script()
    
    # 创建更新的README
    create_updated_readme()
    
    print("\n" + "=" * 50)
    print("🎉 ntwork库内置完成！")
    print("📁 输出目录: dist/企业微信自动回复系统_便携版/")
    print("\n✨ 更新内容:")
    print("- ✅ 内置了ntwork库到项目目录")
    print("- ✅ 更新了gui_app.py以自动加载ntwork")
    print("- ✅ 更新了启动脚本")
    print("- ✅ 更新了使用说明")
    print("\n🚀 现在可以直接运行 start.bat 启动程序！")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
