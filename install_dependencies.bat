@echo off
chcp 65001 >nul
echo 正在安装企业微信自动回复系统依赖...
echo.

echo [1/4] 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] 未找到Python环境，请先安装Python 3.10+
    pause
    exit /b 1
)
echo [OK] Python环境检查通过

echo.
echo [2/4] 安装基础依赖...
pip install PyQt5 requests Pillow dulwich --no-warn-script-location
if %errorlevel% neq 0 (
    echo [ERROR] 基础依赖安装失败
    pause
    exit /b 1
)
echo [OK] 基础依赖安装成功

echo.
echo [3/4] 安装ntwork库...
if exist "ntwork-0.1.3-cp310-cp310-win_amd64.whl" (
    echo 发现本地ntwork wheel文件，正在安装...
    pip install ntwork-0.1.3-cp310-cp310-win_amd64.whl --force-reinstall --no-warn-script-location
    if %errorlevel% equ 0 (
        echo [OK] ntwork库安装成功
    ) else (
        echo [ERROR] ntwork库安装失败
        pause
        exit /b 1
    )
) else (
    echo [ERROR] 未找到ntwork wheel文件
    echo 请确保 ntwork-0.1.3-cp310-cp310-win_amd64.whl 文件存在
    pause
    exit /b 1
)

echo.
echo [4/4] 验证安装...
python -c "import PyQt5; import requests; import PIL; import ntwork; print('[OK] 所有依赖验证通过')" 2>nul
if %errorlevel% neq 0 (
    echo [WARNING] 依赖验证失败，但可能仍然可以运行
)

echo.
echo ========================================
echo 依赖安装完成！
echo 现在可以运行 企业微信自动回复系统.exe
echo ========================================
pause
