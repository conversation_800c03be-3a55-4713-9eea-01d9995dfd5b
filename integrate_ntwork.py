#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
整合ntwork库到exe目录的脚本
"""

import os
import sys
import shutil
from pathlib import Path

def integrate_ntwork_to_exe():
    """将ntwork库整合到exe目录"""
    print("整合ntwork库到exe目录...")
    
    # 源目录和目标目录
    source_dir = Path('dist/企业微信自动回复系统_便携版')
    exe_dir = source_dir / 'dist/企业微信自动回复'
    
    if not exe_dir.exists():
        print(f"exe目录不存在: {exe_dir}")
        return False
    
    # 复制ntwork库到exe目录
    source_ntwork = source_dir / 'ntwork'
    target_ntwork = exe_dir / 'ntwork'
    
    if source_ntwork.exists():
        if target_ntwork.exists():
            shutil.rmtree(target_ntwork)
        shutil.copytree(source_ntwork, target_ntwork)
        print(f"复制ntwork库到: {target_ntwork}")
    
    # 复制相关DLL文件到exe目录
    dll_files = [
        'win32api.pyd',
        'win32gui.pyd', 
        'pythoncom310.dll',
        'pywintypes310.dll'
    ]
    
    for dll_file in dll_files:
        source_dll = source_dir / dll_file
        target_dll = exe_dir / dll_file
        if source_dll.exists():
            shutil.copy2(source_dll, target_dll)
            print(f"复制DLL: {dll_file}")
    
    # 复制项目源码到exe目录
    project_dirs = ['bot', 'bridge', 'channel', 'common', 'gui', 'plugins', 'translate', 'voice']
    
    for proj_dir in project_dirs:
        source_proj = Path(proj_dir)
        target_proj = exe_dir / proj_dir
        
        if source_proj.exists():
            if target_proj.exists():
                shutil.rmtree(target_proj)
            shutil.copytree(source_proj, target_proj, ignore=shutil.ignore_patterns('__pycache__', '*.pyc'))
            print(f"复制项目目录: {proj_dir}")
    
    # 复制配置文件
    config_files = ['config.py', 'config-template.json']
    for config_file in config_files:
        source_config = Path(config_file)
        target_config = exe_dir / config_file
        if source_config.exists():
            shutil.copy2(source_config, target_config)
            print(f"复制配置文件: {config_file}")
    
    # 如果存在config.json，也复制过去
    if Path('config.json').exists():
        shutil.copy2('config.json', exe_dir / 'config.json')
        print("复制了config.json")
    
    return True

def create_exe_startup_script():
    """为exe目录创建启动脚本"""
    exe_dir = Path('dist/企业微信自动回复系统_便携版/dist/企业微信自动回复')
    
    # 创建启动脚本
    start_script = '''@echo off
chcp 65001 >nul
echo 启动企业微信自动回复系统...

echo [INFO] 使用内置ntwork库版本
echo 正在启动程序...
start "" "企业微信自动回复.exe"
'''
    
    with open(exe_dir / 'start.bat', 'w', encoding='utf-8') as f:
        f.write(start_script)
    
    # 创建README
    readme_content = '''# 企业微信自动回复系统 (EXE版本)

## 简介

这是企业微信自动回复系统的exe版本，内置了ntwork库和所有依赖。

## 使用方法

1. 确保企业微信客户端已启动并登录
2. 双击运行 `start.bat` 或直接运行 `企业微信自动回复.exe`
3. 在GUI界面中配置相关参数
4. 点击"启动服务"按钮

## 文件说明

- `企业微信自动回复.exe` - 主程序
- `start.bat` - 启动脚本
- `ntwork/` - 内置ntwork库
- `_internal/` - 程序依赖文件
- `config.json` - 配置文件（如果存在）
- 各种项目源码目录

## 特点

- ✅ 完全独立运行，无需安装Python
- ✅ 内置ntwork库，无需单独安装
- ✅ 包含所有必要依赖
- ✅ 一键启动

## 注意事项

- 确保企业微信客户端已启动并登录
- 首次运行可能需要一些时间来初始化
- 建议在企业微信测试群中先测试功能

## 版本信息

- 版本：EXE内置版 v1.0
- 特点：完全独立，内置所有依赖
- 兼容性：Windows 10/11
'''
    
    with open(exe_dir / 'README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("创建了exe版本的启动脚本和说明")

def create_final_package():
    """创建最终的打包目录"""
    print("创建最终打包目录...")
    
    # 创建最终目录
    final_dir = Path('dist/企业微信自动回复系统_最终版')
    if final_dir.exists():
        shutil.rmtree(final_dir)
    final_dir.mkdir(parents=True)
    
    # 复制exe版本
    exe_source = Path('dist/企业微信自动回复系统_便携版/dist/企业微信自动回复')
    exe_target = final_dir / 'exe版本'
    if exe_source.exists():
        shutil.copytree(exe_source, exe_target)
        print("复制了exe版本")
    
    # 复制便携版本（源码版本）
    portable_source = Path('dist/企业微信自动回复系统_便携版')
    portable_target = final_dir / '便携版本'
    
    # 只复制便携版本的主要文件，排除嵌套的dist目录
    portable_target.mkdir()
    
    items_to_copy = [
        'README.txt', 'start.bat', 'quick_start.bat', 'init_ntwork.py',
        'ntwork', 'win32api.pyd', 'win32gui.pyd', 'pythoncom310.dll', 'pywintypes310.dll'
    ]
    
    # 复制项目源码
    project_dirs = ['bot', 'bridge', 'channel', 'common', 'gui', 'plugins', 'translate', 'voice']
    for proj_dir in project_dirs:
        source_proj = Path(proj_dir)
        if source_proj.exists():
            target_proj = portable_target / proj_dir
            shutil.copytree(source_proj, target_proj, ignore=shutil.ignore_patterns('__pycache__', '*.pyc'))
    
    # 复制其他文件
    other_files = ['gui_app.py', 'config.py', 'config-template.json', '2.ico']
    for file_name in other_files:
        source_file = Path(file_name)
        if source_file.exists():
            shutil.copy2(source_file, portable_target / file_name)
    
    # 复制便携版本的特定文件
    for item in items_to_copy:
        source_item = portable_source / item
        target_item = portable_target / item
        if source_item.exists():
            if source_item.is_dir():
                if not target_item.exists():
                    shutil.copytree(source_item, target_item)
            else:
                shutil.copy2(source_item, target_item)
    
    # 如果存在config.json，复制到两个版本
    if Path('config.json').exists():
        shutil.copy2('config.json', exe_target / 'config.json')
        shutil.copy2('config.json', portable_target / 'config.json')
        print("复制了config.json到两个版本")
    
    # 创建总体说明文件
    main_readme = '''# 企业微信自动回复系统 - 完整打包版

## 包含版本

### 1. exe版本 (推荐)
- 位置：`exe版本/` 目录
- 特点：完全独立运行，无需安装Python
- 使用：直接运行 `企业微信自动回复.exe` 或 `start.bat`

### 2. 便携版本
- 位置：`便携版本/` 目录  
- 特点：需要Python环境，但更灵活
- 使用：运行 `start.bat` 或 `python gui_app.py`

## 推荐使用

**建议优先使用exe版本**，因为：
- ✅ 无需安装Python环境
- ✅ 完全独立运行
- ✅ 内置所有依赖包括ntwork库
- ✅ 启动更简单

## 系统要求

### exe版本
- Windows 10/11
- 企业微信客户端

### 便携版本  
- Windows 10/11
- Python 3.10+
- 企业微信客户端

## 使用说明

1. 选择合适的版本（推荐exe版本）
2. 确保企业微信客户端已启动并登录
3. 运行对应的启动脚本或exe文件
4. 在GUI界面中配置参数
5. 启动服务开始使用

## 注意事项

- 两个版本功能完全相同
- 都内置了ntwork库，无需单独安装
- 建议在测试群中先测试功能
- 如有问题，可以尝试另一个版本

## 技术支持

如遇问题请检查：
1. 企业微信客户端是否正常运行
2. 配置文件是否正确设置
3. 查看程序运行日志
'''
    
    with open(final_dir / 'README.txt', 'w', encoding='utf-8') as f:
        f.write(main_readme)
    
    print(f"创建了最终打包目录: {final_dir}")
    return final_dir

def main():
    """主函数"""
    print("企业微信自动回复系统 - ntwork库整合工具")
    print("=" * 50)
    
    # 整合ntwork到exe目录
    if not integrate_ntwork_to_exe():
        print("整合ntwork库失败")
        return False
    
    # 创建exe启动脚本
    create_exe_startup_script()
    
    # 创建最终打包
    final_dir = create_final_package()
    
    print("\n" + "=" * 50)
    print("🎉 ntwork库整合完成！")
    print(f"📁 最终输出目录: {final_dir}")
    print("\n✨ 包含内容:")
    print("- 📦 exe版本 - 完全独立运行（推荐）")
    print("- 🐍 便携版本 - 需要Python环境")
    print("- 📚 详细使用说明")
    print("\n🚀 使用建议:")
    print("1. 优先使用exe版本（无需Python环境）")
    print("2. 如果exe版本有问题，可使用便携版本")
    print("3. 两个版本都内置了ntwork库")
    print("\n🎯 现在可以分发最终版本目录了！")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
