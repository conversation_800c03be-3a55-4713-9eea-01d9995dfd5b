#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ntwork库路径初始化脚本
"""

import sys
import os

def init_ntwork_path():
    """初始化ntwork库路径"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    ntwork_path = os.path.join(current_dir, 'ntwork')
    
    if os.path.exists(ntwork_path) and ntwork_path not in sys.path:
        sys.path.insert(0, current_dir)
        print(f"已添加ntwork库路径: {ntwork_path}")

if __name__ == "__main__":
    init_ntwork_path()
