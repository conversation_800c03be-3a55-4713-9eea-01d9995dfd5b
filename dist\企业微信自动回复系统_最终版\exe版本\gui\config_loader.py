# encoding:utf-8
"""
GUI版本的配置加载器
简化版本，仅加载必要的配置项
"""

import os
import sys
import json

def setup_gui_config():
    """设置GUI版本的配置"""
    try:
        # 添加项目根目录到Python路径
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if project_root not in sys.path:
            sys.path.append(project_root)

        # 切换到项目根目录，确保load_config能找到配置文件
        original_cwd = os.getcwd()
        os.chdir(project_root)

        try:
            # 导入配置模块
            from config import load_config, conf

            # 加载基础配置
            load_config()
            print("基础配置加载成功")

            # 检查是否存在GUI配置文件
            gui_config_file = os.path.join(project_root, "config-gui.json")
            if os.path.exists(gui_config_file):
                with open(gui_config_file, 'r', encoding='utf-8') as f:
                    gui_config = json.load(f)

                # 更新配置
                for key, value in gui_config.items():
                    conf()[key] = value

                print(f"已加载GUI配置文件: {gui_config_file}")
            else:
                print("GUI配置文件不存在，使用默认配置")

            # 确保必要的配置项存在
            required_configs = {
                "channel_type": "wework",
                "model": "mock",
                "auto_reply_text": "我已经下班，有问题明天再说，急事请电联",
                "single_chat_prefix": [""],
                "group_chat_prefix": ["@bot"],
                "group_name_white_list": ["ALL_GROUP"],
                "debug": True,
                "single_chat_block_list": [],
                "group_chat_block_list": []
            }

            for key, default_value in required_configs.items():
                if key not in conf() or conf()[key] is None:
                    conf()[key] = default_value

            print("GUI配置设置完成")
            return True

        finally:
            # 恢复原始工作目录
            os.chdir(original_cwd)

    except Exception as e:
        print(f"设置GUI配置失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def get_gui_config():
    """获取GUI相关的配置"""
    try:
        from config import conf
        return {
            "auto_reply_text": conf().get("auto_reply_text", "我已经下班，有问题明天再说，急事请电联"),
            "channel_type": conf().get("channel_type", "wework"),
            "model": conf().get("model", "mock"),
            "debug": conf().get("debug", True)
        }
    except Exception as e:
        print(f"获取GUI配置失败: {e}")
        return {
            "auto_reply_text": "我已经下班，有问题明天再说，急事请电联",
            "channel_type": "wework", 
            "model": "mock",
            "debug": True
        }
