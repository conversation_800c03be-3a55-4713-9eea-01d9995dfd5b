#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动打包方案 - 不使用PyInstaller
创建一个便携式的Python应用包
"""

import os
import sys
import shutil
from pathlib import Path

def create_portable_package():
    """创建便携式应用包"""
    print("创建便携式企业微信自动回复系统...")
    
    # 创建输出目录
    output_dir = Path('dist/企业微信自动回复系统_便携版')
    if output_dir.exists():
        shutil.rmtree(output_dir)
    output_dir.mkdir(parents=True)
    
    print(f"输出目录: {output_dir}")
    
    # 复制项目文件
    project_files = [
        'bot', 'bridge', 'channel', 'common', 'gui', 'plugins', 'translate', 'voice',
        'config.py', 'config-template.json', 'gui_app.py', '2.ico'
    ]
    
    for item in project_files:
        src = Path(item)
        if src.exists():
            dst = output_dir / item
            if src.is_dir():
                shutil.copytree(src, dst, ignore=shutil.ignore_patterns('__pycache__', '*.pyc'))
                print(f"复制目录: {item}")
            else:
                shutil.copy2(src, dst)
                print(f"复制文件: {item}")
    
    # 复制ntwork wheel文件
    ntwork_wheel = 'ntwork-0.1.3-cp310-cp310-win_amd64.whl'
    if os.path.exists(ntwork_wheel):
        shutil.copy2(ntwork_wheel, output_dir)
        print(f"复制了 {ntwork_wheel}")
    
    # 复制配置文件
    if os.path.exists('config.json'):
        shutil.copy2('config.json', output_dir)
        print("复制了 config.json")
    elif os.path.exists('config-template.json'):
        shutil.copy2('config-template.json', output_dir / 'config.json')
        print("复制了配置模板为 config.json")
    
    # 创建依赖安装脚本
    install_script = '''@echo off
chcp 65001 >nul
echo 正在安装企业微信自动回复系统依赖...
echo.

echo [1/4] 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] 未找到Python环境，请先安装Python 3.10+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)
echo [OK] Python环境检查通过

echo.
echo [2/4] 安装基础依赖...
pip install PyQt5 requests Pillow dulwich --no-warn-script-location
if %errorlevel% neq 0 (
    echo [ERROR] 基础依赖安装失败
    pause
    exit /b 1
)
echo [OK] 基础依赖安装成功

echo.
echo [3/4] 安装ntwork库...
if exist "ntwork-0.1.3-cp310-cp310-win_amd64.whl" (
    echo 发现本地ntwork wheel文件，正在安装...
    pip install ntwork-0.1.3-cp310-cp310-win_amd64.whl --force-reinstall --no-warn-script-location
    if %errorlevel% equ 0 (
        echo [OK] ntwork库安装成功
    ) else (
        echo [ERROR] ntwork库安装失败
        pause
        exit /b 1
    )
) else (
    echo [ERROR] 未找到ntwork wheel文件
    echo 请确保 ntwork-0.1.3-cp310-cp310-win_amd64.whl 文件存在
    pause
    exit /b 1
)

echo.
echo [4/4] 验证安装...
python -c "import PyQt5; import requests; import PIL; import ntwork; print('[OK] 所有依赖验证通过')" 2>nul
if %errorlevel% neq 0 (
    echo [WARNING] 依赖验证失败，但可能仍然可以运行
)

echo.
echo ========================================
echo 依赖安装完成！
echo 现在可以运行 start.bat 启动程序
echo ========================================
pause
'''
    
    with open(output_dir / 'install_dependencies.bat', 'w', encoding='utf-8') as f:
        f.write(install_script)
    
    # 创建启动脚本
    start_script = '''@echo off
chcp 65001 >nul
echo 启动企业微信自动回复系统...

REM 检查依赖是否已安装
python -c "import PyQt5; import requests; import PIL; import ntwork" 2>nul
if %errorlevel% neq 0 (
    echo [WARNING] 检测到依赖未安装或不完整
    echo 请先运行 install_dependencies.bat 安装依赖
    echo.
    set /p choice="是否现在安装依赖? (y/n): "
    if /i "%choice%"=="y" (
        call install_dependencies.bat
        if %errorlevel% neq 0 exit /b 1
    ) else (
        echo 取消启动
        pause
        exit /b 1
    )
)

echo 正在启动程序...
python gui_app.py
if %errorlevel% neq 0 (
    echo 程序运行出错，请检查错误信息
    pause
)
'''
    
    with open(output_dir / 'start.bat', 'w', encoding='utf-8') as f:
        f.write(start_script)
    
    # 创建快速启动脚本（无依赖检查）
    quick_start_script = '''@echo off
chcp 65001 >nul
echo 快速启动企业微信自动回复系统...
python gui_app.py
if %errorlevel% neq 0 (
    echo 程序运行出错，请检查错误信息
    pause
)
'''
    
    with open(output_dir / 'quick_start.bat', 'w', encoding='utf-8') as f:
        f.write(quick_start_script)
    
    # 创建requirements.txt
    requirements_content = '''PyQt5>=5.15.0
requests>=2.25.0
Pillow>=8.0.0
dulwich>=0.20.0
'''
    
    with open(output_dir / 'requirements.txt', 'w', encoding='utf-8') as f:
        f.write(requirements_content)
    
    # 创建详细的README
    readme_content = '''# 企业微信自动回复系统 (便携版)

## 简介

这是一个便携式的企业微信自动回复系统，无需复杂的打包工具，直接使用Python源码运行。

## 系统要求

- Windows 10/11
- Python 3.10+ (必须安装)
- 企业微信客户端

## 快速开始

### 方式一：一键启动（推荐）
1. 双击运行 `start.bat`
   - 自动检查并安装依赖
   - 启动程序

### 方式二：手动安装
1. 双击运行 `install_dependencies.bat` 安装依赖
2. 编辑 `config.json` 配置文件
3. 双击运行 `quick_start.bat` 启动程序

### 方式三：命令行
```bash
# 安装依赖
pip install -r requirements.txt
pip install ntwork-0.1.3-cp310-cp310-win_amd64.whl

# 启动程序
python gui_app.py
```

## 文件说明

### 启动文件
- `start.bat` - 智能启动脚本（检查依赖+启动）
- `quick_start.bat` - 快速启动脚本（仅启动）
- `install_dependencies.bat` - 依赖安装脚本

### 程序文件
- `gui_app.py` - 主程序入口
- `config.json` - 配置文件
- `2.ico` - 程序图标
- `ntwork-0.1.3-cp310-cp310-win_amd64.whl` - ntwork库文件
- `requirements.txt` - Python依赖列表

### 源码目录
- `bot/` - 机器人模块
- `bridge/` - 桥接模块  
- `channel/` - 通道模块
- `common/` - 公共模块
- `gui/` - 图形界面模块
- `plugins/` - 插件模块
- `translate/` - 翻译模块
- `voice/` - 语音模块

## 配置说明

编辑 `config.json` 文件进行配置：

```json
{
    "channel_type": "wework",
    "wework_smart": true,
    "model": "mock",
    "auto_reply_text": "你好，我是自动回复机器人"
}
```

主要配置项：
- `channel_type`: 通道类型，设置为 "wework"
- `wework_smart`: 企业微信智能模式
- `model`: AI模型类型
- `auto_reply_text`: 自动回复文本

## 依赖说明

### Python依赖
- PyQt5 - GUI界面库
- requests - HTTP请求库
- Pillow - 图像处理库
- dulwich - Git操作库
- ntwork - 企业微信接口库

### 安装方式
所有依赖都会在首次运行时自动安装，也可以手动运行安装脚本。

## 使用说明

1. **启动企业微信客户端**并登录
2. **运行程序**：双击 `start.bat`
3. **配置设置**：在GUI界面中配置相关参数
4. **开始服务**：点击"启动服务"按钮

## 注意事项

- 确保企业微信客户端已启动并登录
- 首次运行需要安装依赖，请保持网络连接
- 如遇问题，请查看控制台错误信息
- 建议在企业微信测试群中先测试功能

## 故障排除

### 常见问题

1. **Python未安装**
   - 下载安装Python 3.10+: https://www.python.org/downloads/
   - 安装时勾选"Add Python to PATH"

2. **依赖安装失败**
   - 检查网络连接
   - 尝试使用国内镜像：`pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/`

3. **企业微信连接失败**
   - 确保企业微信客户端已启动
   - 检查ntwork库是否正确安装
   - 重启企业微信客户端

4. **程序无法启动**
   - 检查Python版本是否为3.10+
   - 查看错误信息并检查配置文件

## 技术支持

如遇问题请检查：
1. Python环境是否正确安装
2. 企业微信客户端是否正常运行  
3. 依赖是否正确安装
4. 配置文件是否正确设置

## 版本信息

- 版本：便携版 v1.0
- 更新日期：2024年
- 兼容性：Windows 10/11 + Python 3.10+
'''
    
    with open(output_dir / 'README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("\n" + "=" * 60)
    print("🎉 便携式打包完成！")
    print(f"📁 输出目录: {output_dir}")
    print("\n✨ 特点:")
    print("- 无需PyInstaller，直接使用Python源码")
    print("- 便携式设计，包含所有必要文件")
    print("- 支持一键安装依赖和启动")
    print("- 完整的源码，便于维护和修改")
    print("\n🚀 使用说明:")
    print("1. 确保已安装Python 3.10+")
    print("2. 进入输出目录")
    print("3. 双击运行 start.bat")
    print("\n📋 文件清单:")
    print("- start.bat (智能启动)")
    print("- quick_start.bat (快速启动)")
    print("- install_dependencies.bat (依赖安装)")
    print("- gui_app.py (主程序)")
    print("- config.json (配置文件)")
    print("- README.txt (详细说明)")
    print("- 完整源码目录")
    
    return True

def main():
    """主函数"""
    print("企业微信自动回复系统 - 便携式打包工具")
    print("=" * 60)
    
    success = create_portable_package()
    
    if not success:
        print("打包失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
