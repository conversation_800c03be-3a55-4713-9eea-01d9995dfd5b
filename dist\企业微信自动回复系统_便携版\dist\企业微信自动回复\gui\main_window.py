# encoding:utf-8
"""
企业微信自动回复系统 - 主窗体
"""

import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QTextEdit, QLineEdit, QPushButton,
                             QLabel, QStatusBar, QGroupBox, QSizePolicy, QMessageBox,
                             QTabWidget, QListWidget, QListWidgetItem, QProgressBar)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QPalette, QColor, QIcon
import os


class MainWindow(QMainWindow):
    """主窗体类"""

    def __init__(self):
        super().__init__()
        # 初始化管理器
        self.log_manager = None
        self.config_manager = None
        self.service_manager = None
        self.system_tray = None

        # 倒计时相关属性
        self.countdown_timer = QTimer()
        self.countdown_seconds = 0
        self.countdown_total_seconds = 0
        self.countdown_label = None
        self.countdown_progress = None
        self.is_countdown_running = False

        # 统计信息相关属性
        self.start_time = None
        self.received_messages = 0
        self.replied_messages = 0
        self.failed_messages = 0
        self.stats_timer = QTimer()
        self.stats_labels = {}

        self.init_managers()
        self.init_ui()
        self.init_system_tray()
        self.init_countdown()
        self.init_stats_manager()

    def init_managers(self):
        """初始化管理器"""
        try:
            from gui.managers import LogManager, ConfigManager, ServiceManager

            # 创建管理器实例
            self.log_manager = LogManager()
            self.config_manager = ConfigManager()
            self.service_manager = ServiceManager()

            # 设置管理器之间的关联
            self.service_manager.set_config_manager(self.config_manager)

            # 连接信号
            self.log_manager.log_message.connect(self.on_log_message)
            self.config_manager.config_updated.connect(self.on_config_updated)
            self.service_manager.service_status_changed.connect(self.on_service_status_changed)
            self.service_manager.service_message.connect(self.on_service_message)
            self.service_manager.service_initialized.connect(self.on_service_initialized)

        except Exception as e:
            print(f"初始化管理器失败: {e}")

    def init_system_tray(self):
        """初始化系统托盘"""
        try:
            from gui.system_tray import SystemTray

            # 创建系统托盘
            self.system_tray = SystemTray(self)

            # 连接托盘信号
            self.system_tray.show_window.connect(self.show_from_tray)
            self.system_tray.hide_window.connect(self.hide_to_tray)
            self.system_tray.start_service.connect(self.on_start_service)
            self.system_tray.stop_service.connect(self.on_stop_service)
            self.system_tray.quit_application.connect(self.close_application)

        except Exception as e:
            print(f"初始化系统托盘失败: {e}")

    def set_window_icon(self):
        """设置窗口图标"""
        try:
            # 获取图标文件路径
            icon_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "2.ico")
            if os.path.exists(icon_path):
                icon = QIcon(icon_path)
                self.setWindowIcon(icon)
                print(f"窗口图标设置成功: {icon_path}")
            else:
                print(f"图标文件不存在: {icon_path}")
        except Exception as e:
            print(f"设置窗口图标失败: {e}")

    def get_main_style(self):
        """获取主样式表"""
        return """
            QMainWindow {
                background-color: #f5f5f5;
            }

            QWidget {
                font-family: "Microsoft YaHei", "Segoe UI", Arial, sans-serif;
                font-size: 9pt;
            }

            /* 卡片样式 */
            QGroupBox {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 6px;
                margin: 4px;
                padding-top: 12px;
                font-weight: bold;
                font-size: 9pt;
                color: #333;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 6px 0 6px;
                color: #2c3e50;
                background-color: white;
            }

            /* 按钮样式 */
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 6px 12px;
                font-weight: bold;
                font-size: 9pt;
                min-height: 18px;
            }

            QPushButton:hover {
                background-color: #2980b9;
                transform: translateY(-1px);
            }

            QPushButton:pressed {
                background-color: #21618c;
                transform: translateY(0px);
            }

            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }

            /* 特殊按钮样式 */
            QPushButton#startButton {
                background-color: #27ae60;
            }

            QPushButton#startButton:hover {
                background-color: #229954;
            }

            QPushButton#stopButton {
                background-color: #e74c3c;
            }

            QPushButton#stopButton:hover {
                background-color: #c0392b;
            }

            QPushButton#statusButton {
                background-color: #f39c12;
            }

            QPushButton#statusButton:hover {
                background-color: #e67e22;
            }

            QPushButton#saveButton {
                background-color: #9b59b6;
            }

            QPushButton#saveButton:hover {
                background-color: #8e44ad;
            }

            QPushButton#helpButton {
                background-color: #17a2b8;
            }

            QPushButton#helpButton:hover {
                background-color: #138496;
            }

            QPushButton#donateButton {
                background-color: #fd7e14;
            }

            QPushButton#donateButton:hover {
                background-color: #e8590c;
            }

            /* 输入框样式 */
            QLineEdit {
                border: 2px solid #ecf0f1;
                border-radius: 6px;
                padding: 10px 12px;
                background-color: white;
                font-size: 9pt;
                min-height: 20px;
            }

            QLineEdit:focus {
                border-color: #3498db;
                outline: none;
            }

            /* 文本区域样式 */
            QTextEdit {
                border: 2px solid #ecf0f1;
                border-radius: 6px;
                background-color: white;
                padding: 8px;
                font-family: "Consolas", "Monaco", monospace;
                font-size: 8pt;
            }

            /* 列表样式 */
            QListWidget {
                border: 2px solid #ecf0f1;
                border-radius: 6px;
                background-color: white;
                padding: 4px;
                outline: none;
            }

            QListWidget::item {
                border-radius: 4px;
                padding: 4px 8px;
                margin: 1px;
            }

            QListWidget::item:selected {
                background-color: #3498db;
                color: white;
            }

            QListWidget::item:hover {
                background-color: #ecf0f1;
            }

            /* 标签页样式 */
            QTabWidget::pane {
                border: 1px solid #e0e0e0;
                border-radius: 6px;
                background-color: white;
                margin-top: -1px;
            }

            QTabBar::tab {
                background-color: #ecf0f1;
                color: #2c3e50;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                font-weight: bold;
            }

            QTabBar::tab:selected {
                background-color: white;
                color: #3498db;
                border-bottom: 2px solid #3498db;
            }

            QTabBar::tab:hover:!selected {
                background-color: #d5dbdb;
            }

            /* 进度条样式 */
            QProgressBar {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                background-color: #ecf0f1;
                text-align: center;
                font-weight: bold;
                color: #2c3e50;
            }

            QProgressBar::chunk {
                background-color: #e67e22;
                border-radius: 3px;
            }

            /* 状态栏样式 */
            QStatusBar {
                background-color: #34495e;
                color: white;
                border-top: 1px solid #2c3e50;
                padding: 4px;
            }
        """

    def init_countdown(self):
        """初始化倒计时功能"""
        self.countdown_timer.timeout.connect(self.update_countdown)

    def init_stats_manager(self):
        """初始化统计管理器"""
        try:
            # 导入统计管理器
            from bot.mock.mock_bot import stats_manager
            # 注册GUI窗口到统计管理器
            stats_manager.set_gui_window(self)
            print("✅ 统计管理器初始化成功")
        except Exception as e:
            print(f"⚠️ 统计管理器初始化失败: {e}")

    def start_countdown(self, seconds=30, message="静默延迟{}s，等待客户端刷新数据，请勿进行任何操作"):
        """启动倒计时"""
        if self.is_countdown_running:
            self.append_log("倒计时已在运行中")
            return

        self.countdown_seconds = seconds
        self.countdown_total_seconds = seconds
        self.is_countdown_running = True
        self.countdown_timer.start(1000)  # 每秒更新一次

        # 显示倒计时控件
        self.countdown_label.show()
        self.countdown_progress.show()
        self.countdown_progress.setMaximum(seconds)
        self.countdown_progress.setValue(0)  # 从0开始，表示还没有经过时间
        print(f"[DEBUG] 开始倒计时: {seconds}秒, 进度条最大值: {self.countdown_progress.maximum()}, 当前值: {self.countdown_progress.value()}")

        # 更新显示
        self.countdown_label.setText(message.format(seconds))

        # 更新按钮状态
        self.start_button.setEnabled(False)
        self.start_button.setText("倒计时中...")
        self.stop_button.setEnabled(True)
        self.stop_button.setText("取消倒计时")

        self.append_log(f"⏰ 开始倒计时: {seconds}秒，等待客户端刷新数据")

    def update_countdown(self):
        """更新倒计时显示"""
        if not self.is_countdown_running:
            return

        self.countdown_seconds -= 1

        if self.countdown_seconds > 0:
            # 更新显示
            self.countdown_label.setText(f"静默延迟{self.countdown_seconds}s，等待客户端刷新数据，请勿进行任何操作")
            # 进度条显示已经过去的时间（从0到总时间）
            elapsed_time = self.countdown_total_seconds - self.countdown_seconds
            self.countdown_progress.setValue(elapsed_time)
            print(f"[DEBUG] 倒计时更新: 剩余{self.countdown_seconds}s, 已过去{elapsed_time}s, 总时间{self.countdown_total_seconds}s")

            # 在特定时间点显示提示
            if self.countdown_seconds == 20:
                self.append_log("⏰ 倒计时剩余20秒")
            elif self.countdown_seconds == 10:
                self.append_log("⏰ 倒计时剩余10秒")
            elif self.countdown_seconds <= 5:
                self.append_log(f"⏰ 倒计时剩余{self.countdown_seconds}秒")
        else:
            # 倒计时结束
            self.stop_countdown()
            self.append_log("✅ 倒计时结束，正在启动企业微信服务...")
            # 启动服务
            self._delayed_start_service()

    def stop_countdown(self, cancelled=False):
        """停止倒计时"""
        if not self.is_countdown_running:
            return

        self.countdown_timer.stop()
        self.countdown_label.hide()
        self.countdown_progress.hide()
        self.is_countdown_running = False

        # 恢复按钮状态
        self.start_button.setText("启动服务")
        self.stop_button.setText("停止服务")

        # 重新启用启动按钮（如果服务未运行）
        if not (self.service_manager and self.service_manager.is_service_running()):
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)

        if cancelled:
            self.append_log("❌ 倒计时已取消")

    def show_help(self):
        """显示帮助窗口"""
        try:
            from gui.help_window import HelpWindow
            help_window = HelpWindow(self)
            help_window.exec_()
        except Exception as e:
            self.append_log(f"打开帮助窗口失败: {e}")

    def show_donate(self):
        """显示请我喝茶窗口"""
        try:
            from gui.donate_window import DonateWindow
            donate_window = DonateWindow(self)
            donate_window.exec_()
        except Exception as e:
            self.append_log(f"打开请我喝茶窗口失败: {e}")

    def update_stats_display(self):
        """更新统计信息显示"""
        try:
            # 更新运行时长
            if self.start_time:
                from datetime import datetime
                runtime = datetime.now() - self.start_time
                hours = int(runtime.total_seconds() // 3600)
                minutes = int((runtime.total_seconds() % 3600) // 60)
                seconds = int(runtime.total_seconds() % 60)
                self.stats_labels['runtime'].setText(f"{hours:02d}:{minutes:02d}:{seconds:02d}")

            # 更新消息统计
            self.stats_labels['received'].setText(str(self.received_messages))
            self.stats_labels['replied'].setText(str(self.replied_messages))

            # 计算成功率
            if self.received_messages > 0:
                success_rate = ((self.received_messages - self.failed_messages) / self.received_messages) * 100
                self.stats_labels['success_rate'].setText(f"{success_rate:.1f}%")
            else:
                self.stats_labels['success_rate'].setText("100%")

        except Exception as e:
            print(f"更新统计信息失败: {e}")

    def start_stats_tracking(self):
        """开始统计跟踪"""
        from datetime import datetime
        self.start_time = datetime.now()
        self.received_messages = 0
        self.replied_messages = 0
        self.failed_messages = 0
        self.stats_timer.start(1000)  # 每秒更新一次
        self.append_log("📊 开始统计跟踪")

    def stop_stats_tracking(self):
        """停止统计跟踪"""
        self.stats_timer.stop()
        self.start_time = None
        self.append_log("📊 停止统计跟踪")

    def increment_received_messages(self):
        """增加接收消息计数"""
        self.received_messages += 1

    def increment_replied_messages(self):
        """增加回复消息计数"""
        self.replied_messages += 1

    def increment_failed_messages(self):
        """增加失败消息计数"""
        self.failed_messages += 1

    def init_ui(self):
        """初始化用户界面"""
        # 设置窗体基本属性
        self.setWindowTitle("企业微信自动回复系统")
        self.setGeometry(100, 100, 750, 600)  # 减小窗口大小
        self.setMinimumSize(700, 550)  # 减小最小尺寸

        # 设置窗口图标
        self.set_window_icon()

        # 设置整体样式
        self.setStyleSheet(self.get_main_style())

        # 创建中央窗体
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(3)
        main_layout.setContentsMargins(3, 3, 3, 3)

        # 创建主标签页
        self.main_tabs = QTabWidget()
        main_layout.addWidget(self.main_tabs)

        # 创建运行界面标签页
        self.create_runtime_tab()

        # 创建配置界面标签页
        self.create_config_tab()

        # 创建状态栏
        self.create_status_bar()

    def create_runtime_tab(self):
        """创建运行界面标签页"""
        # 创建运行界面容器
        runtime_widget = QWidget()
        runtime_layout = QVBoxLayout(runtime_widget)
        runtime_layout.setSpacing(6)
        runtime_layout.setContentsMargins(6, 6, 6, 6)

        # 创建控制按钮区域
        self.create_control_buttons(runtime_layout)

        # 创建倒计时显示区域
        self.create_countdown_area(runtime_layout)

        # 创建统计信息区域
        self.create_stats_area(runtime_layout)

        # 创建日志显示区域
        self.create_log_area(runtime_layout)

        # 添加到主标签页
        self.main_tabs.addTab(runtime_widget, "🚀 运行界面")

    def create_config_tab(self):
        """创建配置界面标签页"""
        # 创建配置界面容器
        config_widget = QWidget()
        config_layout = QVBoxLayout(config_widget)
        config_layout.setSpacing(6)
        config_layout.setContentsMargins(6, 6, 6, 6)

        # 创建配置设置区域
        self.create_config_area(config_layout)

        # 添加到主标签页
        self.main_tabs.addTab(config_widget, "⚙️ 配置界面")

    def create_control_buttons(self, parent_layout):
        """创建控制按钮区域"""
        # 创建按钮布局
        button_layout = QHBoxLayout()

        # 启动服务按钮
        self.start_button = QPushButton("🚀 启动服务")
        self.start_button.setObjectName("startButton")
        self.start_button.setMinimumHeight(32)
        self.start_button.setMinimumWidth(100)
        self.start_button.clicked.connect(self.on_start_service)

        # 停止服务按钮
        self.stop_button = QPushButton("⏹️ 停止服务")
        self.stop_button.setObjectName("stopButton")
        self.stop_button.setMinimumHeight(32)
        self.stop_button.setMinimumWidth(100)
        self.stop_button.setEnabled(False)
        self.stop_button.clicked.connect(self.on_stop_service)

        # 状态显示按钮
        self.status_button = QPushButton("📊 已停止")
        self.status_button.setObjectName("statusButton")
        self.status_button.setMinimumHeight(32)
        self.status_button.setMinimumWidth(100)
        self.status_button.setEnabled(False)

        # 帮助按钮
        self.help_button = QPushButton("❓ 使用说明")
        self.help_button.setObjectName("helpButton")
        self.help_button.setMinimumHeight(32)
        self.help_button.setMinimumWidth(100)
        self.help_button.clicked.connect(self.show_help)

        # 请我喝茶按钮
        self.donate_button = QPushButton("☕ 请我喝茶")
        self.donate_button.setObjectName("donateButton")
        self.donate_button.setMinimumHeight(32)
        self.donate_button.setMinimumWidth(100)
        self.donate_button.clicked.connect(self.show_donate)

        # 添加按钮到布局
        button_layout.addWidget(self.start_button)
        button_layout.addWidget(self.stop_button)
        button_layout.addWidget(self.status_button)
        button_layout.addStretch()  # 添加弹性空间
        button_layout.addWidget(self.help_button)
        button_layout.addWidget(self.donate_button)

        parent_layout.addLayout(button_layout)

        # 添加倒计时显示区域
        self.create_countdown_area(parent_layout)

    def create_countdown_area(self, parent_layout):
        """创建倒计时显示区域"""
        # 倒计时标签
        self.countdown_label = QLabel("")
        self.countdown_label.setAlignment(Qt.AlignCenter)
        self.countdown_label.setStyleSheet("""
            QLabel {
                color: #e67e22;
                font-size: 11pt;
                font-weight: bold;
                padding: 12px;
                background-color: #fef9e7;
                border: 2px solid #f39c12;
                border-radius: 8px;
                margin: 4px;
            }
        """)
        self.countdown_label.hide()  # 初始隐藏

        # 倒计时进度条
        self.countdown_progress = QProgressBar()
        self.countdown_progress.setMinimum(0)
        self.countdown_progress.setMaximum(30)
        self.countdown_progress.setValue(0)
        self.countdown_progress.setTextVisible(False)
        self.countdown_progress.setFixedHeight(25)
        self.countdown_progress.setTextVisible(True)  # 显示百分比文字
        self.countdown_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #f39c12;
                border-radius: 8px;
                background-color: #fef9e7;
                margin: 4px;
                text-align: center;
                font-weight: bold;
                color: #2c3e50;
            }
            QProgressBar::chunk {
                background-color: #f39c12;
                border-radius: 6px;
            }
        """)
        self.countdown_progress.hide()  # 初始隐藏

        parent_layout.addWidget(self.countdown_label)
        parent_layout.addWidget(self.countdown_progress)

    def create_stats_area(self, parent_layout):
        """创建统计信息显示区域"""
        # 创建统计信息组框
        stats_group = QGroupBox("📊 运行统计")
        stats_layout = QHBoxLayout(stats_group)

        # 运行时长
        runtime_layout = QVBoxLayout()
        runtime_title = QLabel("运行时长")
        runtime_title.setAlignment(Qt.AlignCenter)
        runtime_title.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.stats_labels['runtime'] = QLabel("00:00:00")
        self.stats_labels['runtime'].setAlignment(Qt.AlignCenter)
        self.stats_labels['runtime'].setStyleSheet("font-size: 12pt; color: #27ae60; font-weight: bold;")
        runtime_layout.addWidget(runtime_title)
        runtime_layout.addWidget(self.stats_labels['runtime'])

        # 接收消息数
        received_layout = QVBoxLayout()
        received_title = QLabel("接收消息")
        received_title.setAlignment(Qt.AlignCenter)
        received_title.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.stats_labels['received'] = QLabel("0")
        self.stats_labels['received'].setAlignment(Qt.AlignCenter)
        self.stats_labels['received'].setStyleSheet("font-size: 12pt; color: #3498db; font-weight: bold;")
        received_layout.addWidget(received_title)
        received_layout.addWidget(self.stats_labels['received'])

        # 回复消息数
        replied_layout = QVBoxLayout()
        replied_title = QLabel("回复消息")
        replied_title.setAlignment(Qt.AlignCenter)
        replied_title.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.stats_labels['replied'] = QLabel("0")
        self.stats_labels['replied'].setAlignment(Qt.AlignCenter)
        self.stats_labels['replied'].setStyleSheet("font-size: 12pt; color: #e67e22; font-weight: bold;")
        replied_layout.addWidget(replied_title)
        replied_layout.addWidget(self.stats_labels['replied'])

        # 成功率
        success_layout = QVBoxLayout()
        success_title = QLabel("成功率")
        success_title.setAlignment(Qt.AlignCenter)
        success_title.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.stats_labels['success_rate'] = QLabel("100%")
        self.stats_labels['success_rate'].setAlignment(Qt.AlignCenter)
        self.stats_labels['success_rate'].setStyleSheet("font-size: 12pt; color: #9b59b6; font-weight: bold;")
        success_layout.addWidget(success_title)
        success_layout.addWidget(self.stats_labels['success_rate'])

        # 添加到主布局
        stats_layout.addLayout(runtime_layout)
        stats_layout.addLayout(received_layout)
        stats_layout.addLayout(replied_layout)
        stats_layout.addLayout(success_layout)

        parent_layout.addWidget(stats_group)

        # 初始化统计定时器
        self.stats_timer.timeout.connect(self.update_stats_display)

    def create_log_area(self, parent_layout):
        """创建日志显示区域"""
        # 创建日志组框
        log_group = QGroupBox("📋 运行日志")
        log_layout = QVBoxLayout(log_group)

        # 创建日志文本框
        self.log_text = QTextEdit()
        self.log_text.setMinimumHeight(150)
        self.log_text.setReadOnly(True)

        # 设置日志文本框样式 (黑色背景，绿色文字)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #000000;
                color: #00ff00;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10pt;
                border: 1px solid #ccc;
            }
        """)

        # 添加初始日志
        self.log_text.append("系统启动，等待用户操作...")
        self.log_text.append("提示：点击'启动服务'开始企业微信自动回复")
        self.log_text.append("提示：可以最小化到系统托盘运行")

        log_layout.addWidget(self.log_text)
        parent_layout.addWidget(log_group)

    def create_config_area(self, parent_layout):
        """创建配置设置区域"""
        # 创建基本配置区域
        self.create_basic_config_area(parent_layout)

        # 创建屏蔽配置区域
        self.create_block_config_area(parent_layout)

    def create_basic_config_area(self, parent_layout):
        """创建基本配置区域"""
        # 创建基本配置组框
        basic_group = QGroupBox("📝 基本配置")
        basic_layout = QVBoxLayout(basic_group)

        # 创建回复内容设置
        reply_layout = QHBoxLayout()
        reply_label = QLabel("回复内容:")
        reply_label.setMinimumWidth(80)

        self.reply_input = QLineEdit()
        # 从配置管理器加载回复内容
        if self.config_manager:
            reply_text = self.config_manager.get_config("auto_reply_text", "我已经下班，有问题明天再说，急事请电联")
            self.reply_input.setText(reply_text)
        else:
            self.reply_input.setText("我已经下班，有问题明天再说，急事请电联")
        self.reply_input.setMinimumHeight(36)

        self.save_button = QPushButton("💾 保存配置")
        self.save_button.setObjectName("saveButton")
        self.save_button.setMinimumHeight(28)
        self.save_button.setMinimumWidth(90)
        self.save_button.clicked.connect(self.on_save_config)

        reply_layout.addWidget(reply_label)
        reply_layout.addWidget(self.reply_input)
        reply_layout.addWidget(self.save_button)

        basic_layout.addLayout(reply_layout)
        parent_layout.addWidget(basic_group)

    def create_block_config_area(self, parent_layout):
        """创建屏蔽配置区域"""
        # 创建屏蔽配置组框
        block_group = QGroupBox("🚫 屏蔽设置")
        block_layout = QVBoxLayout(block_group)

        # 调用原有的屏蔽配置创建方法
        self.create_block_config_content(block_layout)

        parent_layout.addWidget(block_group)

    def create_block_config_content(self, parent_layout):
        """创建屏蔽配置内容"""

        # 单聊屏蔽设置
        single_chat_group = QGroupBox("👤 单聊屏蔽列表")
        single_chat_layout = QVBoxLayout(single_chat_group)

        # 单聊屏蔽列表
        self.single_chat_block_list = QListWidget()
        self.single_chat_block_list.setMinimumHeight(60)
        self.single_chat_block_list.setMaximumHeight(90)
        single_chat_layout.addWidget(self.single_chat_block_list)

        # 单聊添加/删除按钮
        single_chat_btn_layout = QHBoxLayout()
        self.single_chat_input = QLineEdit()
        self.single_chat_input.setPlaceholderText("输入要屏蔽的用户昵称")
        self.single_chat_input.setMinimumHeight(32)
        add_single_btn = QPushButton("添加")
        add_single_btn.setMinimumHeight(32)
        add_single_btn.setMaximumWidth(60)
        add_single_btn.clicked.connect(self.add_single_chat_block)
        remove_single_btn = QPushButton("删除选中")
        remove_single_btn.setMinimumHeight(32)
        remove_single_btn.setMaximumWidth(80)
        remove_single_btn.clicked.connect(self.remove_single_chat_block)

        single_chat_btn_layout.addWidget(self.single_chat_input)
        single_chat_btn_layout.addWidget(add_single_btn)
        single_chat_btn_layout.addWidget(remove_single_btn)
        single_chat_layout.addLayout(single_chat_btn_layout)

        parent_layout.addWidget(single_chat_group)

        # 群聊屏蔽设置
        group_chat_group = QGroupBox("👥 群聊屏蔽列表")
        group_chat_layout = QVBoxLayout(group_chat_group)

        # 群聊屏蔽列表
        self.group_chat_block_list = QListWidget()
        self.group_chat_block_list.setMinimumHeight(60)
        self.group_chat_block_list.setMaximumHeight(90)
        group_chat_layout.addWidget(self.group_chat_block_list)

        # 群聊添加/删除按钮
        group_chat_btn_layout = QHBoxLayout()
        self.group_chat_input = QLineEdit()
        self.group_chat_input.setPlaceholderText("输入要屏蔽的群聊名称")
        self.group_chat_input.setMinimumHeight(32)
        add_group_btn = QPushButton("添加")
        add_group_btn.setMinimumHeight(32)
        add_group_btn.setMaximumWidth(60)
        add_group_btn.clicked.connect(self.add_group_chat_block)
        remove_group_btn = QPushButton("删除选中")
        remove_group_btn.setMinimumHeight(32)
        remove_group_btn.setMaximumWidth(80)
        remove_group_btn.clicked.connect(self.remove_group_chat_block)

        group_chat_btn_layout.addWidget(self.group_chat_input)
        group_chat_btn_layout.addWidget(add_group_btn)
        group_chat_btn_layout.addWidget(remove_group_btn)
        group_chat_layout.addLayout(group_chat_btn_layout)

        parent_layout.addWidget(group_chat_group)

        # 保存屏蔽配置按钮
        save_block_btn = QPushButton("💾 保存屏蔽配置")
        save_block_btn.setObjectName("saveButton")
        save_block_btn.setMinimumHeight(28)
        save_block_btn.clicked.connect(self.save_block_config)
        parent_layout.addWidget(save_block_btn)

        # 加载现有的屏蔽配置
        self.load_block_config()

    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.status_bar.showMessage("企业微信自动回复系统 v1.0")
        self.setStatusBar(self.status_bar)

    def on_start_service(self):
        """启动服务按钮点击事件"""
        if self.service_manager:
            # 启动倒计时，倒计时结束后会自动调用_delayed_start_service
            self.start_countdown(30)
        else:
            self.append_log("错误: 服务管理器未初始化")

    def _delayed_start_service(self):
        """延迟启动服务"""
        if self.service_manager:
            self.service_manager.start_wework_service()
            # 启动统计跟踪
            self.start_stats_tracking()
        else:
            self.append_log("错误: 服务管理器未初始化")

    def on_stop_service(self):
        """停止服务按钮点击事件"""
        # 如果倒计时正在运行，则取消倒计时
        if self.is_countdown_running:
            self.stop_countdown(cancelled=True)
            return

        # 否则停止服务
        if self.service_manager:
            self.service_manager.stop_wework_service()
            # 停止统计跟踪
            self.stop_stats_tracking()
        else:
            self.append_log("错误: 服务管理器未初始化")

    def on_save_config(self):
        """保存配置按钮点击事件"""
        reply_text = self.reply_input.text().strip()
        if not reply_text:
            self.append_log("错误: 回复内容不能为空")
            QMessageBox.warning(self, "配置错误", "回复内容不能为空，请输入有效的回复文本。")
            return

        if len(reply_text) > 500:
            self.append_log("警告: 回复内容过长，建议控制在500字符以内")
            reply = QMessageBox.question(self, "内容过长",
                                       f"回复内容较长({len(reply_text)}字符)，建议控制在500字符以内。\n是否继续保存？",
                                       QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.No:
                return

        if self.config_manager:
            self.config_manager.set_config("auto_reply_text", reply_text)
            if self.config_manager.save_config():
                self.append_log(f"✅ 配置保存成功: {reply_text[:50]}{'...' if len(reply_text) > 50 else ''}")
                QMessageBox.information(self, "保存成功", "配置已成功保存！")
            else:
                self.append_log("❌ 配置保存失败")
                QMessageBox.critical(self, "保存失败", "配置保存失败，请检查文件权限。")
        else:
            self.append_log("错误: 配置管理器未初始化")
            QMessageBox.critical(self, "系统错误", "配置管理器未初始化，请重启应用程序。")

    def update_service_status(self, is_running):
        """更新服务状态"""
        if is_running:
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.status_button.setText("运行中")
            self.status_button.setStyleSheet("QPushButton { background-color: #90EE90; }")
        else:
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.status_button.setText("已停止")
            self.status_button.setStyleSheet("QPushButton { background-color: #f0f0f0; }")

        # 更新系统托盘状态
        if self.system_tray:
            self.system_tray.update_service_status(is_running)

    def append_log(self, message):
        """添加日志消息"""
        self.log_text.append(message)
        # 自动滚动到底部
        self.log_text.moveCursor(self.log_text.textCursor().End)

    # 信号处理方法
    def on_log_message(self, message):
        """处理日志消息信号"""
        self.log_text.append(message)

        # 检查是否是初始化完成的消息
        if "wework程序初始化完成" in message:
            # 延迟1秒后清空日志，确保用户能看到完成消息
            QTimer.singleShot(1000, self.on_service_initialized)
        # 自动滚动到底部
        self.log_text.moveCursor(self.log_text.textCursor().End)

    def on_config_updated(self, config_data):
        """处理配置更新信号"""
        self.append_log("配置已更新")

    def on_service_initialized(self):
        """处理服务初始化完成信号"""
        # 清空日志记录
        self.log_text.clear()
        self.append_log("🎉 企业微信程序初始化完成")
        self.append_log("📝 日志已清空，准备显示新的消息记录")
        self.append_log("👀 等待接收消息...")

    def on_service_status_changed(self, is_running):
        """处理服务状态变化信号"""
        self.update_service_status(is_running)

    def on_service_message(self, message):
        """处理服务消息信号"""
        self.append_log(message)

    # 系统托盘相关方法
    def show_from_tray(self):
        """从托盘显示窗口"""
        self.show()
        self.raise_()
        self.activateWindow()

    def hide_to_tray(self):
        """隐藏到托盘"""
        self.hide()
        if self.system_tray:
            self.system_tray.show_message("企业微信自动回复系统", "程序已最小化到系统托盘")

    def close_application(self):
        """关闭应用程序"""
        # 停止服务
        if self.service_manager and self.service_manager.is_service_running():
            self.service_manager.stop_wework_service()

        # 隐藏托盘图标
        if self.system_tray:
            self.system_tray.hide_tray()

        # 退出应用
        QApplication.quit()

    def closeEvent(self, event):
        """重写关闭事件，最小化到托盘而不是退出"""
        if self.system_tray and self.system_tray.tray_icon.isVisible():
            # 最小化到托盘
            self.hide_to_tray()
            event.ignore()
        else:
            # 没有托盘支持，直接退出
            self.close_application()
            event.accept()

    # 屏蔽配置相关方法
    def load_block_config(self):
        """加载屏蔽配置"""
        if not self.config_manager:
            return

        # 加载单聊屏蔽列表
        single_chat_blocks = self.config_manager.get_config("single_chat_block_list", [])
        self.single_chat_block_list.clear()
        for user in single_chat_blocks:
            self.single_chat_block_list.addItem(user)

        # 加载群聊屏蔽列表
        group_chat_blocks = self.config_manager.get_config("group_chat_block_list", [])
        self.group_chat_block_list.clear()
        for group in group_chat_blocks:
            self.group_chat_block_list.addItem(group)

    def add_single_chat_block(self):
        """添加单聊屏蔽"""
        user_name = self.single_chat_input.text().strip()
        if not user_name:
            QMessageBox.warning(self, "输入错误", "请输入要屏蔽的用户昵称")
            return

        # 检查是否已存在
        for i in range(self.single_chat_block_list.count()):
            if self.single_chat_block_list.item(i).text() == user_name:
                QMessageBox.information(self, "重复添加", f"用户 '{user_name}' 已在屏蔽列表中")
                return

        # 添加到列表
        self.single_chat_block_list.addItem(user_name)
        self.single_chat_input.clear()
        self.append_log(f"已添加单聊屏蔽: {user_name}")

    def remove_single_chat_block(self):
        """删除单聊屏蔽"""
        current_item = self.single_chat_block_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "选择错误", "请选择要删除的用户")
            return

        user_name = current_item.text()
        self.single_chat_block_list.takeItem(self.single_chat_block_list.row(current_item))
        self.append_log(f"已删除单聊屏蔽: {user_name}")

    def add_group_chat_block(self):
        """添加群聊屏蔽"""
        group_name = self.group_chat_input.text().strip()
        if not group_name:
            QMessageBox.warning(self, "输入错误", "请输入要屏蔽的群聊名称")
            return

        # 检查是否已存在
        for i in range(self.group_chat_block_list.count()):
            if self.group_chat_block_list.item(i).text() == group_name:
                QMessageBox.information(self, "重复添加", f"群聊 '{group_name}' 已在屏蔽列表中")
                return

        # 添加到列表
        self.group_chat_block_list.addItem(group_name)
        self.group_chat_input.clear()
        self.append_log(f"已添加群聊屏蔽: {group_name}")

    def remove_group_chat_block(self):
        """删除群聊屏蔽"""
        current_item = self.group_chat_block_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "选择错误", "请选择要删除的群聊")
            return

        group_name = current_item.text()
        self.group_chat_block_list.takeItem(self.group_chat_block_list.row(current_item))
        self.append_log(f"已删除群聊屏蔽: {group_name}")

    def save_block_config(self):
        """保存屏蔽配置"""
        if not self.config_manager:
            QMessageBox.critical(self, "系统错误", "配置管理器未初始化")
            return

        # 收集单聊屏蔽列表
        single_chat_blocks = []
        for i in range(self.single_chat_block_list.count()):
            single_chat_blocks.append(self.single_chat_block_list.item(i).text())

        # 收集群聊屏蔽列表
        group_chat_blocks = []
        for i in range(self.group_chat_block_list.count()):
            group_chat_blocks.append(self.group_chat_block_list.item(i).text())

        # 保存到配置
        self.config_manager.set_config("single_chat_block_list", single_chat_blocks)
        self.config_manager.set_config("group_chat_block_list", group_chat_blocks)

        if self.config_manager.save_config():
            self.append_log(f"✅ 屏蔽配置保存成功")
            self.append_log(f"单聊屏蔽: {len(single_chat_blocks)} 个用户")
            self.append_log(f"群聊屏蔽: {len(group_chat_blocks)} 个群聊")
            QMessageBox.information(self, "保存成功", "屏蔽配置已成功保存！")
        else:
            self.append_log("❌ 屏蔽配置保存失败")
            QMessageBox.critical(self, "保存失败", "屏蔽配置保存失败，请检查文件权限。")


def main():
    """测试主窗体"""
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
