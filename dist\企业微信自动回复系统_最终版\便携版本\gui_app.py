# encoding:utf-8
"""
企业微信自动回复GUI应用主入口
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """
    GUI应用主函数
    """
    print("企业微信自动回复GUI应用启动中...")

    # 验证PyQt是否可用
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtGui import QIcon
        from gui.main_window import MainWindow
        print("✅ PyQt5 依赖检查通过")

        # 创建应用实例
        app = QApplication(sys.argv)
        app.setApplicationName("企业微信自动回复系统")
        app.setApplicationVersion("1.0")

        # 设置应用图标
        try:
            icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "2.ico")
            if os.path.exists(icon_path):
                app.setWindowIcon(QIcon(icon_path))
                print(f"✅ 应用图标设置成功: {icon_path}")
            else:
                print(f"⚠️ 图标文件不存在: {icon_path}")
        except Exception as e:
            print(f"⚠️ 设置应用图标失败: {e}")

        # 创建主窗体
        main_window = MainWindow()
        main_window.show()

        print("✅ GUI界面已启动")

        # 运行应用
        sys.exit(app.exec_())

    except ImportError as e:
        print(f"❌ PyQt5 依赖检查失败: {e}")
        print("请运行: pip install -r requirements.txt")
        return False
    except Exception as e:
        print(f"❌ GUI启动失败: {e}")
        return False

    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
