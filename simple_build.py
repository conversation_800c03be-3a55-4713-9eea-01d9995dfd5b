#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的打包脚本 - 避免复杂的依赖分析
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def create_minimal_gui():
    """创建最小化的GUI启动文件"""
    print("创建最小化GUI启动文件...")
    
    minimal_gui_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小化GUI启动器 - 避免复杂依赖
"""

import sys
import os

def main():
    """最小化GUI启动函数"""
    print("企业微信自动回复系统启动中...")
    
    try:
        # 检查PyQt5
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtGui import QIcon
        print("PyQt5 检查通过")
        
        # 动态导入GUI模块
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        try:
            from gui.main_window import MainWindow
            print("GUI模块导入成功")
        except ImportError as e:
            print(f"GUI模块导入失败: {e}")
            print("请确保所有依赖已正确安装")
            input("按回车键退出...")
            return False
        
        # 创建应用
        app = QApplication(sys.argv)
        app.setApplicationName("企业微信自动回复系统")
        app.setApplicationVersion("1.0")
        
        # 设置图标
        try:
            icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "2.ico")
            if os.path.exists(icon_path):
                app.setWindowIcon(QIcon(icon_path))
        except Exception as e:
            print(f"设置图标失败: {e}")
        
        # 创建主窗体
        main_window = MainWindow()
        main_window.show()
        
        print("GUI界面已启动")
        
        # 运行应用
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"依赖检查失败: {e}")
        print("请运行 install_dependencies.bat 安装依赖")
        input("按回车键退出...")
        return False
    except Exception as e:
        print(f"启动失败: {e}")
        input("按回车键退出...")
        return False

if __name__ == "__main__":
    main()
'''
    
    with open('minimal_gui.py', 'w', encoding='utf-8') as f:
        f.write(minimal_gui_content)
    
    print("创建了 minimal_gui.py")

def build_minimal():
    """使用最简单的方式构建"""
    print("开始最小化构建...")
    
    # 清理之前的构建
    if os.path.exists('dist'):
        print("清理之前的构建文件...")
        shutil.rmtree('dist')
    
    if os.path.exists('build'):
        shutil.rmtree('build')
    
    # 使用最基本的PyInstaller命令
    cmd = [
        'pyinstaller',
        '--onedir',           # 单目录模式
        '--windowed',         # 无控制台窗口
        '--name=企业微信自动回复系统',
        '--icon=2.ico',
        '--add-data=2.ico;.',
        '--add-data=config-template.json;.',
        '--exclude-module=ntwork',
        '--exclude-module=requests',
        '--exclude-module=urllib3',
        '--exclude-module=certifi',
        '--exclude-module=PIL',
        '--exclude-module=dulwich',
        '--exclude-module=cryptography',
        '--exclude-module=numpy',
        '--exclude-module=scipy',
        '--exclude-module=pandas',
        '--exclude-module=matplotlib',
        '--exclude-module=tkinter',
        'minimal_gui.py'
    ]
    
    print("运行PyInstaller...")
    print(f"命令: {' '.join(cmd)}")
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"PyInstaller失败:")
        print(result.stderr)
        return False
    
    print("PyInstaller构建成功")
    return True

def copy_project_files():
    """复制项目文件到构建目录"""
    print("复制项目文件...")
    
    dist_dir = Path('dist/企业微信自动回复系统')
    if not dist_dir.exists():
        print("构建目录不存在")
        return False
    
    # 复制整个项目目录结构（除了不需要的）
    exclude_dirs = {'dist', 'build', '__pycache__', '.git', 'tmp'}
    exclude_files = {'*.pyc', '*.pyo', '*.spec'}
    
    project_files = [
        'bot', 'bridge', 'channel', 'common', 'gui', 'plugins', 'translate', 'voice',
        'config.py', 'config-template.json'
    ]
    
    for item in project_files:
        src = Path(item)
        if src.exists():
            dst = dist_dir / item
            if src.is_dir():
                if src.name not in exclude_dirs:
                    shutil.copytree(src, dst, ignore=shutil.ignore_patterns('__pycache__', '*.pyc'))
                    print(f"复制目录: {item}")
            else:
                shutil.copy2(src, dst)
                print(f"复制文件: {item}")
    
    return True

def create_dependencies_scripts():
    """创建依赖安装脚本"""
    print("创建依赖安装脚本...")
    
    dist_dir = Path('dist/企业微信自动回复系统')
    
    # 依赖安装脚本
    install_script = '''@echo off
chcp 65001 >nul
echo 正在安装企业微信自动回复系统依赖...
echo.

echo [1/4] 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] 未找到Python环境，请先安装Python 3.10+
    pause
    exit /b 1
)
echo [OK] Python环境检查通过

echo.
echo [2/4] 安装基础依赖...
pip install PyQt5 requests Pillow dulwich --no-warn-script-location
if %errorlevel% neq 0 (
    echo [ERROR] 基础依赖安装失败
    pause
    exit /b 1
)
echo [OK] 基础依赖安装成功

echo.
echo [3/4] 安装ntwork库...
if exist "ntwork-0.1.3-cp310-cp310-win_amd64.whl" (
    echo 发现本地ntwork wheel文件，正在安装...
    pip install ntwork-0.1.3-cp310-cp310-win_amd64.whl --force-reinstall --no-warn-script-location
    if %errorlevel% equ 0 (
        echo [OK] ntwork库安装成功
    ) else (
        echo [ERROR] ntwork库安装失败
        pause
        exit /b 1
    )
) else (
    echo [ERROR] 未找到ntwork wheel文件
    echo 请确保 ntwork-0.1.3-cp310-cp310-win_amd64.whl 文件存在
    pause
    exit /b 1
)

echo.
echo [4/4] 验证安装...
python -c "import PyQt5; import requests; import PIL; import ntwork; print('[OK] 所有依赖验证通过')" 2>nul
if %errorlevel% neq 0 (
    echo [WARNING] 依赖验证失败，但可能仍然可以运行
)

echo.
echo ========================================
echo 依赖安装完成！
echo 现在可以运行 企业微信自动回复系统.exe
echo ========================================
pause
'''
    
    with open(dist_dir / 'install_dependencies.bat', 'w', encoding='utf-8') as f:
        f.write(install_script)
    
    # 启动脚本
    start_script = '''@echo off
chcp 65001 >nul
echo 启动企业微信自动回复系统...

REM 检查依赖是否已安装
python -c "import PyQt5; import requests; import PIL; import ntwork" 2>nul
if %errorlevel% neq 0 (
    echo [WARNING] 检测到依赖未安装或不完整
    echo 请先运行 install_dependencies.bat 安装依赖
    echo.
    set /p choice="是否现在安装依赖? (y/n): "
    if /i "%choice%"=="y" (
        call install_dependencies.bat
        if %errorlevel% neq 0 exit /b 1
    ) else (
        echo 取消启动
        pause
        exit /b 1
    )
)

echo 正在启动程序...
start "" "企业微信自动回复系统.exe"
'''
    
    with open(dist_dir / 'start.bat', 'w', encoding='utf-8') as f:
        f.write(start_script)
    
    # 复制ntwork wheel文件
    ntwork_wheel = 'ntwork-0.1.3-cp310-cp310-win_amd64.whl'
    if os.path.exists(ntwork_wheel):
        shutil.copy2(ntwork_wheel, dist_dir)
        print(f"复制了 {ntwork_wheel}")
    
    # 复制配置文件
    if os.path.exists('config.json'):
        shutil.copy2('config.json', dist_dir)
        print("复制了 config.json")
    elif os.path.exists('config-template.json'):
        shutil.copy2('config-template.json', dist_dir / 'config.json')
        print("复制了配置模板为 config.json")
    
    # 创建README
    readme_content = '''# 企业微信自动回复系统 (轻量化版本)

## 快速开始：

方式一（推荐）：
1. 双击运行 `start.bat` - 自动检查并安装依赖，然后启动程序

方式二（手动）：
1. 双击运行 `install_dependencies.bat` 安装所有依赖
2. 编辑 `config.json` 配置文件
3. 双击运行 `企业微信自动回复系统.exe`

## 特点：

- exe文件轻量化，所有复杂依赖作为外部文件
- 支持一键安装依赖和启动
- 便于维护和更新
- 避免了复杂的依赖打包问题

## 注意事项：

- 确保已安装Python 3.10+环境
- 确保企业微信客户端已安装并登录
- 所有复杂依赖都作为外部文件，不打包在exe中
- 配置文件需要根据实际情况修改

## 文件说明：

- `企业微信自动回复系统.exe` - 主程序（轻量化）
- `start.bat` - 一键启动脚本（推荐使用）
- `install_dependencies.bat` - 依赖安装脚本
- `ntwork-0.1.3-cp310-cp310-win_amd64.whl` - ntwork库文件
- `config.json` - 配置文件
- `bot/`, `gui/`, `channel/` 等 - 项目源码目录
- `_internal/` - 程序核心文件目录

## 依赖说明：

外部依赖（需要单独安装）：
- PyQt5 - GUI界面库
- requests - HTTP请求库  
- Pillow - 图像处理库
- dulwich - Git操作库
- ntwork - 企业微信接口库

## 技术支持：

如遇问题请检查：
1. Python环境是否正确安装
2. 企业微信客户端是否正常运行
3. 依赖是否正确安装（运行install_dependencies.bat）
4. 配置文件是否正确设置
'''
    
    with open(dist_dir / 'README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("创建了依赖安装脚本和说明文件")

def main():
    """主函数"""
    print("企业微信自动回复系统 - 轻量化打包工具")
    print("=" * 60)
    
    # 创建最小化GUI文件
    create_minimal_gui()
    
    # 构建应用
    if not build_minimal():
        print("构建失败")
        return False
    
    # 复制项目文件
    if not copy_project_files():
        print("复制项目文件失败")
        return False
    
    # 创建依赖脚本
    create_dependencies_scripts()
    
    print("\n" + "=" * 60)
    print("🎉 轻量化打包完成！")
    print(f"📁 输出目录: dist/企业微信自动回复系统/")
    print("\n✨ 特点:")
    print("- exe文件轻量化，避免复杂依赖打包问题")
    print("- 项目源码作为外部文件，便于维护")
    print("- 支持一键安装依赖和启动")
    print("\n🚀 使用说明:")
    print("1. 进入 dist/企业微信自动回复系统/ 目录")
    print("2. 双击运行 start.bat（推荐）")
    print("   或先运行 install_dependencies.bat 再运行 exe")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
