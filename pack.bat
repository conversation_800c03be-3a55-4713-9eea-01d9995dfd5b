@echo off
chcp 65001
echo 🚀 企业微信自动回复系统打包工具
echo ================================================

echo 📦 开始打包...
python build.py

if %errorlevel% equ 0 (
    echo.
    echo ✅ 打包成功完成！
    echo 📁 输出目录: dist\企业微信自动回复系统\
    echo.
    echo 使用说明:
    echo 1. 进入 dist\企业微信自动回复系统\ 目录
    echo 2. 先运行 install_ntwork.bat 安装ntwork库
    echo 3. 配置 config.json 文件  
    echo 4. 运行 企业微信自动回复系统.exe
) else (
    echo ❌ 打包失败，请检查错误信息
)

echo.
pause
