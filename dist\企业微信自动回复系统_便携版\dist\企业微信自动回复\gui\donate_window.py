# encoding:utf-8

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QScrollArea, QWidget)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QPixmap, QIcon
import os

class DonateWindow(QDialog):
    """请我喝茶窗口"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("☕ 请我喝茶")
        self.setGeometry(300, 300, 500, 600)
        self.setMinimumSize(450, 550)
        self.setMaximumSize(600, 700)
        
        # 设置窗口图标
        self.set_window_icon()
        
        # 设置样式
        self.setStyleSheet(self.get_style())
        
        # 创建界面
        self.init_ui()
        
    def set_window_icon(self):
        """设置窗口图标"""
        try:
            icon_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "2.ico")
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
        except Exception as e:
            print(f"设置请我喝茶窗口图标失败: {e}")
    
    def get_style(self):
        """获取样式表"""
        return """
            QDialog {
                background-color: #f8f9fa;
            }
            
            QLabel {
                color: #2c3e50;
            }
            
            QPushButton {
                background-color: #fd7e14;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 10pt;
                min-height: 20px;
            }
            
            QPushButton:hover {
                background-color: #e8590c;
            }
            
            QPushButton:pressed {
                background-color: #dc5200;
            }
            
            QScrollArea {
                border: none;
                background-color: transparent;
            }
        """
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # 标题
        title_label = QLabel("☕ 请我喝茶")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            font-size: 20pt; 
            font-weight: bold; 
            color: #fd7e14; 
            margin: 10px 0 20px 0;
        """)
        scroll_layout.addWidget(title_label)
        
        # 感谢文字
        thanks_label = QLabel()
        thanks_label.setAlignment(Qt.AlignCenter)
        thanks_label.setWordWrap(True)
        thanks_label.setStyleSheet("""
            font-size: 12pt; 
            color: #2c3e50; 
            line-height: 1.6;
            margin: 0 0 20px 0;
            padding: 15px;
            background-color: #fff3cd;
            border-radius: 8px;
            border: 1px solid #ffeaa7;
        """)
        thanks_label.setText("""
🎉 感谢您使用企业微信自动回复系统！

如果这个小工具帮助到了您，节省了您的时间，
让您的工作更加轻松，那我就很开心了！

如果您愿意请我喝杯茶，我会非常感激！
您的支持是我继续优化和维护这个项目的动力。

☕ 一杯茶的温暖，一份代码的情怀 ☕
        """)
        scroll_layout.addWidget(thanks_label)
        
        # 二维码图片
        image_label = QLabel()
        image_label.setAlignment(Qt.AlignCenter)
        image_label.setStyleSheet("""
            border: 3px solid #fd7e14;
            border-radius: 12px;
            padding: 15px;
            background-color: white;
            margin: 10px;
        """)
        
        # 尝试加载图片
        try:
            image_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "33.png")
            if os.path.exists(image_path):
                pixmap = QPixmap(image_path)
                if not pixmap.isNull():
                    # 缩放图片到合适大小
                    scaled_pixmap = pixmap.scaled(280, 280, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    image_label.setPixmap(scaled_pixmap)
                else:
                    image_label.setText("☕ 请我喝茶二维码\n(33.png)")
                    image_label.setStyleSheet(image_label.styleSheet() + "font-size: 14pt; color: #7f8c8d;")
            else:
                image_label.setText("☕ 请我喝茶二维码\n(33.png 文件未找到)")
                image_label.setStyleSheet(image_label.styleSheet() + "font-size: 12pt; color: #e74c3c;")
        except Exception as e:
            image_label.setText(f"☕ 请我喝茶二维码\n(加载失败: {str(e)})")
            image_label.setStyleSheet(image_label.styleSheet() + "font-size: 11pt; color: #e74c3c;")
        
        image_label.setMinimumHeight(320)
        scroll_layout.addWidget(image_label)
        
        # 说明文字
        info_label = QLabel()
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("""
            font-size: 11pt; 
            color: #6c757d; 
            margin: 15px 0;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 6px;
        """)
        info_label.setText("""
💡 扫描上方二维码即可请我喝茶

无论金额大小，都是对我工作的认可和鼓励！
您的每一份支持都会让这个项目变得更好！

🙏 再次感谢您的使用和支持！
        """)
        scroll_layout.addWidget(info_label)
        
        scroll_layout.addStretch()
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        
        layout.addWidget(scroll_area)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        close_button = QPushButton("关闭")
        close_button.clicked.connect(self.close)
        button_layout.addWidget(close_button)
        
        layout.addLayout(button_layout)
