#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小化GUI启动器 - 避免复杂依赖
"""

import sys
import os

def main():
    """最小化GUI启动函数"""
    print("企业微信自动回复系统启动中...")
    
    try:
        # 检查PyQt5
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtGui import QIcon
        print("PyQt5 检查通过")
        
        # 动态导入GUI模块
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        try:
            from gui.main_window import MainWindow
            print("GUI模块导入成功")
        except ImportError as e:
            print(f"GUI模块导入失败: {e}")
            print("请确保所有依赖已正确安装")
            input("按回车键退出...")
            return False
        
        # 创建应用
        app = QApplication(sys.argv)
        app.setApplicationName("企业微信自动回复系统")
        app.setApplicationVersion("1.0")
        
        # 设置图标
        try:
            icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "2.ico")
            if os.path.exists(icon_path):
                app.setWindowIcon(QIcon(icon_path))
        except Exception as e:
            print(f"设置图标失败: {e}")
        
        # 创建主窗体
        main_window = MainWindow()
        main_window.show()
        
        print("GUI界面已启动")
        
        # 运行应用
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"依赖检查失败: {e}")
        print("请运行 install_dependencies.bat 安装依赖")
        input("按回车键退出...")
        return False
    except Exception as e:
        print(f"启动失败: {e}")
        input("按回车键退出...")
        return False

if __name__ == "__main__":
    main()
