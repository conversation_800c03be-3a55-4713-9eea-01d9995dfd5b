# encoding:utf-8
"""
企业微信自动回复系统 - 管理器模块
包含日志管理器、配置管理器、服务管理器
"""

import logging
import json
import os
import threading
import time
from collections import deque
from datetime import datetime
from PyQt5.QtCore import QObject, pyqtSignal


class LogManager(QObject):
    """日志管理器 - 将日志输出重定向到GUI"""

    # 定义信号，用于向GUI发送日志消息
    log_message = pyqtSignal(str)

    def __init__(self, max_lines=1000):
        super().__init__()
        self.max_lines = max_lines
        self.log_buffer = deque(maxlen=max_lines)
        self.gui_handler = None
        self.setup_gui_handler()

    def setup_gui_handler(self):
        """设置GUI日志处理器"""
        # 创建自定义日志处理器
        self.gui_handler = GUILogHandler(self)
        self.gui_handler.setLevel(logging.INFO)

        # 设置日志格式
        formatter = logging.Formatter(
            '[%(levelname)s][%(asctime)s] %(message)s',
            datefmt='%H:%M:%S'
        )
        self.gui_handler.setFormatter(formatter)

        # 获取根日志记录器并添加处理器
        root_logger = logging.getLogger()
        root_logger.addHandler(self.gui_handler)
        root_logger.setLevel(logging.INFO)

    def format_log_message(self, record):
        """格式化日志消息"""
        try:
            message = self.gui_handler.format(record)
            return message
        except Exception:
            return f"[ERROR] 日志格式化失败: {record.getMessage()}"

    def add_log_message(self, message):
        """添加日志消息到缓冲区并发送信号"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"

        # 添加到缓冲区
        self.log_buffer.append(formatted_message)

        # 发送信号到GUI
        self.log_message.emit(formatted_message)

    def get_log_history(self):
        """获取日志历史"""
        return list(self.log_buffer)

    def clear_log_buffer(self):
        """清空日志缓冲区"""
        self.log_buffer.clear()


class GUILogHandler(logging.Handler):
    """自定义日志处理器，将日志发送到GUI"""

    def __init__(self, log_manager):
        super().__init__()
        self.log_manager = log_manager

    def emit(self, record):
        """处理日志记录"""
        try:
            message = self.format(record)
            # 通过日志管理器发送到GUI
            self.log_manager.log_message.emit(message)
        except Exception:
            # 避免日志处理器本身出错
            pass


class ConfigManager(QObject):
    """配置管理器 - 管理应用配置"""

    # 配置更新信号
    config_updated = pyqtSignal(dict)

    def __init__(self, config_file="config-gui.json"):
        super().__init__()
        self.config_file = config_file
        self.config_data = {}
        self.load_config()

    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config_data = json.load(f)
            else:
                # 使用默认配置
                self.config_data = self.get_default_config()
                self.save_config()
            return True
        except Exception as e:
            print(f"加载配置失败: {e}")
            self.config_data = self.get_default_config()
            return False

    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, ensure_ascii=False, indent=2)

            # 同时更新全局配置，确保立即生效
            try:
                from config import conf
                for key, value in self.config_data.items():
                    conf()[key] = value
                print(f"全局配置已更新: {self.config_data}")
            except Exception as e:
                print(f"更新全局配置失败: {e}")

            self.config_updated.emit(self.config_data.copy())
            return True
        except Exception as e:
            print(f"保存配置失败: {e}")
            return False

    def get_config(self, key, default=None):
        """获取配置项"""
        return self.config_data.get(key, default)

    def set_config(self, key, value):
        """设置配置项"""
        self.config_data[key] = value

    def get_default_config(self):
        """获取默认配置"""
        return {
            "channel_type": "wework",
            "model": "mock",
            "auto_reply_text": "我已经下班，有问题明天再说，急事请电联",
            "single_chat_prefix": [""],
            "single_chat_reply_prefix": "",
            "group_chat_prefix": ["@bot"],
            "group_name_white_list": ["ALL_GROUP"],
            "debug": True,
            "image_recognition": False,
            "speech_recognition": False,
            "voice_reply_voice": False,
            "single_chat_block_list": [],
            "group_chat_block_list": []
        }


class ServiceManager(QObject):
    """服务管理器 - 管理企业微信服务的启停"""

    # 服务状态变化信号
    service_status_changed = pyqtSignal(bool)  # True=运行中, False=已停止
    service_message = pyqtSignal(str)  # 服务消息
    service_initialized = pyqtSignal()  # 服务初始化完成信号

    def __init__(self):
        super().__init__()
        self.is_running = False
        self.service_thread = None
        self.wework_channel = None
        self.config_manager = None

    def set_config_manager(self, config_manager):
        """设置配置管理器"""
        self.config_manager = config_manager

    def start_wework_service(self):
        """启动企业微信服务"""
        if self.is_running:
            self.service_message.emit("服务已在运行中")
            return True

        try:
            self.service_message.emit("正在启动企业微信服务...")

            # 在新线程中启动服务
            self.service_thread = threading.Thread(
                target=self._run_service,
                daemon=True
            )
            self.service_thread.start()

            return True
        except Exception as e:
            self.service_message.emit(f"启动服务失败: {e}")
            return False

    def stop_wework_service(self):
        """停止企业微信服务"""
        if not self.is_running:
            self.service_message.emit("服务未运行")
            return True

        try:
            self.service_message.emit("正在停止企业微信服务...")
            self.is_running = False

            # 停止企业微信服务
            if self.wework_channel:
                try:
                    # 调用ntwork的退出方法
                    import ntwork
                    ntwork.exit_()
                    self.service_message.emit("企业微信客户端已退出")
                except Exception as e:
                    self.service_message.emit(f"退出企业微信客户端失败: {e}")

            self.wework_channel = None
            self.service_status_changed.emit(False)
            self.service_message.emit("企业微信服务已停止")
            return True
        except Exception as e:
            self.service_message.emit(f"停止服务失败: {e}")
            return False

    def _run_service(self):
        """在后台线程中运行服务"""
        try:
            # 设置配置
            self._setup_config()

            # 创建并启动企业微信通道
            self.service_message.emit("正在初始化企业微信通道...")

            # 导入必要的模块
            import sys
            import os

            # 添加项目根目录到路径
            project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            if project_root not in sys.path:
                sys.path.append(project_root)

            # 导入企业微信通道
            from channel.wework.wework_channel import WeworkChannel

            # Bridge是单例类，会自动根据配置创建相应的bot
            # 不需要手动创建和绑定bot
            self.service_message.emit("消息处理系统准备就绪")

            # 创建企业微信通道
            self.wework_channel = WeworkChannel()
            self.service_message.emit("企业微信通道创建成功")

            # 启动服务
            self.is_running = True
            self.service_status_changed.emit(True)
            self.service_message.emit("正在启动企业微信客户端...")
            self.service_message.emit("请确保企业微信客户端已启动并登录")

            try:
                # 启动企业微信通道（这会阻塞线程）
                # 注意：startup()方法会在最后调用run.forever()进入无限循环
                # 所以我们需要通过其他方式检测初始化完成
                import threading

                # 创建一个线程来监听初始化完成
                def check_initialization():
                    import time
                    # 等待一段时间让startup开始执行
                    time.sleep(2)

                    # 检查wework_channel是否已经初始化
                    max_wait = 60  # 最多等待60秒
                    waited = 0
                    while waited < max_wait:
                        if hasattr(self.wework_channel, 'inited') and self.wework_channel.inited:
                            # 初始化完成
                            self.service_message.emit("企业微信程序初始化完成")
                            self.service_initialized.emit()
                            break
                        time.sleep(1)
                        waited += 1

                    if waited >= max_wait:
                        self.service_message.emit("等待初始化超时")

                # 启动检查线程
                check_thread = threading.Thread(target=check_initialization)
                check_thread.daemon = True
                check_thread.start()

                # 启动企业微信通道
                self.wework_channel.startup()
            except Exception as e:
                self.service_message.emit(f"企业微信服务启动失败: {e}")
                self.service_message.emit("请检查：1.企业微信客户端是否已启动 2.ntwork库是否正确安装")
                self.is_running = False
                self.service_status_changed.emit(False)
                raise e

        except ImportError as e:
            self.service_message.emit(f"导入模块失败: {e}")
            self.service_message.emit("请确保已安装ntwork库: pip install ntwork")
            self.is_running = False
            self.service_status_changed.emit(False)
        except Exception as e:
            self.service_message.emit(f"服务运行异常: {e}")
            self.is_running = False
            self.service_status_changed.emit(False)

    def _setup_config(self):
        """设置配置"""
        try:
            # 使用GUI配置加载器
            from gui.config_loader import setup_gui_config

            # 设置GUI配置
            if setup_gui_config():
                self.service_message.emit("配置加载成功")
            else:
                self.service_message.emit("配置加载失败，使用默认配置")

            # 如果有配置管理器，更新配置
            if self.config_manager:
                from config import conf
                auto_reply_text = self.config_manager.get_config("auto_reply_text")
                if auto_reply_text:
                    # 更新全局配置
                    conf()["auto_reply_text"] = auto_reply_text
                    self.service_message.emit(f"已设置自动回复内容: {auto_reply_text}")

        except Exception as e:
            self.service_message.emit(f"配置设置失败: {e}")

    def is_service_running(self):
        """检查服务是否运行中"""
        return self.is_running

    def get_service_info(self):
        """获取服务信息"""
        return {
            "is_running": self.is_running,
            "service_type": "wework",
            "thread_active": self.service_thread and self.service_thread.is_alive()
        }
