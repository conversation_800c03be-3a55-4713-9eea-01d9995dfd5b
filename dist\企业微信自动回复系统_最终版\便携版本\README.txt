# 企业微信自动回复系统 (内置ntwork版)

## 简介

这是一个内置了ntwork库的企业微信自动回复系统便携版，无需手动安装ntwork库。

## 系统要求

- Windows 10/11
- Python 3.10+ (必须安装)
- 企业微信客户端

## 快速开始

### 方式一：一键启动（推荐）
1. 双击运行 `start.bat`
   - 自动检查并安装基础依赖
   - 启动程序

### 方式二：直接启动
1. 双击运行 `quick_start.bat`
   - 直接启动程序（需要已安装基础依赖）

### 方式三：命令行
```bash
# 安装基础依赖（首次运行）
pip install PyQt5 requests Pillow dulwich

# 启动程序
python gui_app.py
```

## 文件说明

### 启动文件
- `start.bat` - 智能启动脚本（检查依赖+启动）
- `quick_start.bat` - 快速启动脚本（仅启动）

### 程序文件
- `gui_app.py` - 主程序入口
- `config.json` - 配置文件
- `2.ico` - 程序图标
- `ntwork/` - 内置ntwork库目录

### 源码目录
- `bot/` - 机器人模块
- `bridge/` - 桥接模块  
- `channel/` - 通道模块
- `common/` - 公共模块
- `gui/` - 图形界面模块
- `plugins/` - 插件模块
- `translate/` - 翻译模块
- `voice/` - 语音模块

## 特点

- ✅ **内置ntwork库** - 无需手动安装ntwork
- ✅ **便携式设计** - 包含所有必要文件
- ✅ **一键启动** - 自动处理依赖
- ✅ **完整源码** - 便于维护和修改

## 配置说明

编辑 `config.json` 文件进行配置：

```json
{
    "channel_type": "wework",
    "wework_smart": true,
    "model": "mock",
    "auto_reply_text": "你好，我是自动回复机器人"
}
```

## 使用说明

1. **启动企业微信客户端**并登录
2. **运行程序**：双击 `start.bat`
3. **配置设置**：在GUI界面中配置相关参数
4. **开始服务**：点击"启动服务"按钮

## 注意事项

- 确保企业微信客户端已启动并登录
- 首次运行可能需要安装基础依赖
- ntwork库已内置，无需单独安装
- 建议在企业微信测试群中先测试功能

## 故障排除

### 常见问题

1. **Python未安装**
   - 下载安装Python 3.10+: https://www.python.org/downloads/
   - 安装时勾选"Add Python to PATH"

2. **基础依赖安装失败**
   - 检查网络连接
   - 尝试使用国内镜像：`pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/`

3. **企业微信连接失败**
   - 确保企业微信客户端已启动
   - 重启企业微信客户端

4. **程序无法启动**
   - 检查Python版本是否为3.10+
   - 查看错误信息并检查配置文件

## 版本信息

- 版本：内置ntwork版 v1.0
- 特点：内置ntwork库，无需手动安装
- 兼容性：Windows 10/11 + Python 3.10+
