@echo off
chcp 65001 >nul
echo 启动企业微信自动回复系统...

REM 检查Python环境
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] 未找到Python环境，请先安装Python 3.10+
    pause
    exit /b 1
)

REM 检查基础依赖
python -c "import PyQt5; import requests; import PIL" 2>nul
if %errorlevel% neq 0 (
    echo [WARNING] 检测到基础依赖未安装
    echo 正在尝试安装基础依赖...
    pip install PyQt5 requests Pillow dulwich --no-warn-script-location
    if %errorlevel% neq 0 (
        echo [ERROR] 基础依赖安装失败
        pause
        exit /b 1
    )
)

echo [INFO] ntwork库已内置，无需单独安装
echo 正在启动程序...
python gui_app.py
if %errorlevel% neq 0 (
    echo 程序运行出错，请检查错误信息
    pause
)
